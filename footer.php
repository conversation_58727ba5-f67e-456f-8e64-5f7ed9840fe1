<?php

/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package TechNews_Pro
 */

?>

</main><!-- #main -->

<!-- Footer -->
<footer id="colophon" class="site-footer bg-dark text-white mt-5">

	<!-- Footer Widgets -->
	<div class="footer-widgets py-5">
		<div class="container">
			<div class="row">
				<?php
				$footer_widgets = array();
				for ($i = 1; $i <= 4; $i++) {
					if (is_active_sidebar('footer-' . $i)) {
						$footer_widgets[] = $i;
					}
				}

				$widget_count = count($footer_widgets);
				$col_class = 'col-lg-3 col-md-6';

				if ($widget_count == 1) {
					$col_class = 'col-12';
				} elseif ($widget_count == 2) {
					$col_class = 'col-lg-6 col-md-6';
				} elseif ($widget_count == 3) {
					$col_class = 'col-lg-4 col-md-6';
				}

				foreach ($footer_widgets as $widget_num) :
				?>
					<div class="<?php echo esc_attr($col_class); ?> mb-4">
						<?php dynamic_sidebar('footer-' . $widget_num); ?>
					</div>
				<?php endforeach; ?>

				<?php if (empty($footer_widgets)) : ?>
					<!-- Default Footer Content -->
					<div class="col-lg-3 col-md-6 mb-4">
						<div class="footer-widget">
							<h4 class="widget-title h6 mb-3 text-white">
								<?php esc_html_e('About TechNews Pro', 'technews-pro'); ?>
							</h4>
							<p class="text-light">
								<?php esc_html_e('Your trusted source for the latest technology news, reviews, and insights. Stay updated with cutting-edge innovations and industry trends.', 'technews-pro'); ?>
							</p>
							<div class="social-links mt-3">
								<a href="#" class="text-light me-3" aria-label="Facebook">
									<i class="fab fa-facebook-f"></i>
								</a>
								<a href="#" class="text-light me-3" aria-label="Twitter">
									<i class="fab fa-twitter"></i>
								</a>
								<a href="#" class="text-light me-3" aria-label="LinkedIn">
									<i class="fab fa-linkedin-in"></i>
								</a>
								<a href="#" class="text-light me-3" aria-label="YouTube">
									<i class="fab fa-youtube"></i>
								</a>
							</div>
						</div>
					</div>

					<div class="col-lg-3 col-md-6 mb-4">
						<div class="footer-widget">
							<h4 class="widget-title h6 mb-3 text-white">
								<?php esc_html_e('Quick Links', 'technews-pro'); ?>
							</h4>
							<ul class="list-unstyled">
								<li class="mb-2">
									<a href="<?php echo esc_url(home_url('/')); ?>" class="text-light text-decoration-none">
										<i class="fas fa-chevron-<?php echo is_rtl() ? 'left' : 'right'; ?> me-2 small"></i>
										<?php esc_html_e('Home', 'technews-pro'); ?>
									</a>
								</li>
								<li class="mb-2">
									<a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>" class="text-light text-decoration-none">
										<i class="fas fa-chevron-<?php echo is_rtl() ? 'left' : 'right'; ?> me-2 small"></i>
										<?php esc_html_e('News', 'technews-pro'); ?>
									</a>
								</li>
								<li class="mb-2">
									<a href="#" class="text-light text-decoration-none">
										<i class="fas fa-chevron-<?php echo is_rtl() ? 'left' : 'right'; ?> me-2 small"></i>
										<?php esc_html_e('About Us', 'technews-pro'); ?>
									</a>
								</li>
								<li class="mb-2">
									<a href="#" class="text-light text-decoration-none">
										<i class="fas fa-chevron-<?php echo is_rtl() ? 'left' : 'right'; ?> me-2 small"></i>
										<?php esc_html_e('Contact', 'technews-pro'); ?>
									</a>
								</li>
							</ul>
						</div>
					</div>

					<div class="col-lg-3 col-md-6 mb-4">
						<div class="footer-widget">
							<h4 class="widget-title h6 mb-3 text-white">
								<?php esc_html_e('Categories', 'technews-pro'); ?>
							</h4>
							<ul class="list-unstyled">
								<?php
								$categories = get_categories(array(
									'orderby' => 'count',
									'order'   => 'DESC',
									'number'  => 5,
								));

								foreach ($categories as $category) :
								?>
									<li class="mb-2">
										<a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" class="text-light text-decoration-none">
											<i class="fas fa-chevron-<?php echo is_rtl() ? 'left' : 'right'; ?> me-2 small"></i>
											<?php echo esc_html($category->name); ?>
											<span class="badge bg-secondary ms-2"><?php echo esc_html($category->count); ?></span>
										</a>
									</li>
								<?php endforeach; ?>
							</ul>
						</div>
					</div>

					<div class="col-lg-3 col-md-6 mb-4">
						<div class="footer-widget">
							<h4 class="widget-title h6 mb-3 text-white">
								<?php esc_html_e('Recent Posts', 'technews-pro'); ?>
							</h4>
							<?php
							$recent_posts = wp_get_recent_posts(array(
								'numberposts' => 3,
								'post_status' => 'publish'
							));

							if ($recent_posts) :
							?>
								<div class="recent-posts">
									<?php foreach ($recent_posts as $post) : ?>
										<div class="recent-post-item d-flex mb-3">
											<?php if (has_post_thumbnail($post['ID'])) : ?>
												<div class="recent-post-thumb me-3">
													<a href="<?php echo esc_url(get_permalink($post['ID'])); ?>">
														<img src="<?php echo esc_url(get_the_post_thumbnail_url($post['ID'], 'technews-thumbnail')); ?>" alt="<?php echo esc_attr($post['post_title']); ?>" class="rounded" style="width: 60px; height: 60px; object-fit: cover;">
													</a>
												</div>
											<?php endif; ?>
											<div class="recent-post-content">
												<h6 class="recent-post-title mb-1">
													<a href="<?php echo esc_url(get_permalink($post['ID'])); ?>" class="text-light text-decoration-none">
														<?php echo esc_html(wp_trim_words($post['post_title'], 6)); ?>
													</a>
												</h6>
												<small class="text-muted">
													<i class="far fa-calendar-alt me-1"></i>
													<?php echo esc_html(get_the_date('', $post['ID'])); ?>
												</small>
											</div>
										</div>
									<?php endforeach; ?>
								</div>
							<?php endif; ?>
						</div>
					</div>
				<?php endif; ?>
			</div>
		</div>
	</div>

	<!-- Footer Bottom -->
	<div class="footer-bottom bg-darker py-3">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-md-6">
					<div class="footer-info">
						<p class="mb-0 text-light">
							&copy; <?php echo date('Y'); ?>
							<a href="<?php echo esc_url(home_url('/')); ?>" class="text-white text-decoration-none">
								<?php bloginfo('name'); ?>
							</a>.
							<?php esc_html_e('All rights reserved.', 'technews-pro'); ?>
						</p>
					</div>
				</div>
				<div class="col-md-6">
					<div class="footer-menu text-md-end">
						<?php
						wp_nav_menu(array(
							'theme_location' => 'footer',
							'menu_class'     => 'footer-nav list-unstyled d-inline-flex mb-0',
							'container'      => false,
							'depth'          => 1,
							'fallback_cb'    => false,
							'link_before'    => '',
							'link_after'     => '',
							'walker'         => new class extends Walker_Nav_Menu {
								function start_el(&$output, $item, $depth = 0, $args = null, $id = 0)
								{
									$output .= '<li class="me-3"><a href="' . esc_url($item->url) . '" class="text-light text-decoration-none small">' . esc_html($item->title) . '</a></li>';
								}
								function end_el(&$output, $item, $depth = 0, $args = null)
								{
									// No closing tag needed for our custom structure
								}
							}
						));
						?>
					</div>
				</div>
			</div>
		</div>
	</div>
</footer><!-- #colophon -->

<!-- Back to Top Button -->
<button id="backToTop" class="btn btn-primary position-fixed bottom-0 end-0 m-4 rounded-circle" style="display: none; z-index: 1000;" aria-label="<?php esc_attr_e('Back to top', 'technews-pro'); ?>">
	<i class="fas fa-chevron-up"></i>
</button>

</div><!-- #page -->

<?php wp_footer(); ?>

<!-- Custom JavaScript for Back to Top -->
<script>
	document.addEventListener('DOMContentLoaded', function() {
		const backToTopButton = document.getElementById('backToTop');

		window.addEventListener('scroll', function() {
			if (window.pageYOffset > 300) {
				backToTopButton.style.display = 'block';
			} else {
				backToTopButton.style.display = 'none';
			}
		});

		backToTopButton.addEventListener('click', function() {
			window.scrollTo({
				top: 0,
				behavior: 'smooth'
			});
		});
	});
</script>

</body>

</html>