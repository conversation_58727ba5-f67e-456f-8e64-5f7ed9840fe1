<?php

/**
 * The template for displaying search results pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#search-result
 *
 * @package TechNews_Pro
 */

get_header();
?>

<div class="container">
	<div class="row">
		<!-- Main Content -->
		<div class="col-lg-8 col-md-12">
			<main id="primary" class="site-main">

				<?php if (have_posts()) : ?>

					<!-- Search Header -->
					<header class="page-header mb-5">
						<div class="search-header-content bg-light p-4 rounded">
							<h1 class="page-title h2 mb-3">
								<?php
								printf(
									/* translators: %s: search query. */
									esc_html__('Search Results for: %s', 'technews-pro'),
									'<span class="text-primary">' . get_search_query() . '</span>'
								);
								?>
							</h1>

							<!-- Search Meta -->
							<div class="search-meta">
								<div class="d-flex flex-wrap align-items-center gap-3">
									<small class="text-muted">
										<i class="fas fa-search me-1"></i>
										<?php
										global $wp_query;
										$total_results = $wp_query->found_posts;
										printf(
											esc_html(_n('%s result found', '%s results found', $total_results, 'technews-pro')),
											number_format_i18n($total_results)
										);
										?>
									</small>

									<small class="text-muted">
										<i class="far fa-clock me-1"></i>
										<?php esc_html_e('Search completed', 'technews-pro'); ?>
									</small>
								</div>
							</div>

							<!-- Search Again -->
							<div class="search-again mt-3">
								<details>
									<summary class="btn btn-outline-primary btn-sm">
										<i class="fas fa-search me-1"></i>
										<?php esc_html_e('Search Again', 'technews-pro'); ?>
									</summary>
									<div class="mt-3">
										<?php get_search_form(); ?>
									</div>
								</details>
							</div>
						</div>
					</header>

					<!-- Search Results -->
					<div class="search-results">
						<div class="row">
							<?php
							/* Start the Loop */
							while (have_posts()) :
								the_post();
							?>
								<div class="col-lg-6 col-md-6 mb-4">
									<article id="post-<?php the_ID(); ?>" <?php post_class('search-result-card h-100'); ?>>
										<div class="card h-100 border-0 shadow-sm">
											<?php if (has_post_thumbnail()) : ?>
												<div class="card-img-top position-relative overflow-hidden">
													<a href="<?php the_permalink(); ?>">
														<?php the_post_thumbnail('technews-grid', array('class' => 'w-100', 'style' => 'height: 180px; object-fit: cover;')); ?>
													</a>

													<!-- Post Type Badge -->
													<div class="position-absolute top-0 start-0 m-2">
														<span class="badge bg-secondary">
															<?php echo esc_html(get_post_type_object(get_post_type())->labels->singular_name); ?>
														</span>
													</div>

													<!-- Category Badge -->
													<?php if ('post' === get_post_type()) : ?>
														<div class="position-absolute top-0 end-0 m-2">
															<?php
															$categories = get_the_category();
															if (! empty($categories)) :
															?>
																<span class="badge bg-primary"><?php echo esc_html($categories[0]->name); ?></span>
															<?php endif; ?>
														</div>
													<?php endif; ?>
												</div>
											<?php endif; ?>

											<div class="card-body p-3">
												<!-- Post Meta -->
												<div class="post-meta mb-2">
													<small class="text-muted">
														<i class="far fa-calendar-alt me-1"></i>
														<?php echo get_the_date(); ?>

														<?php if ('post' === get_post_type()) : ?>
															<span class="mx-2">•</span>
															<i class="far fa-user me-1"></i>
															<?php the_author_posts_link(); ?>
														<?php endif; ?>

														<span class="mx-2">•</span>
														<i class="far fa-eye me-1"></i>
														<?php echo technews_pro_get_post_views(get_the_ID()); ?>
													</small>
												</div>

												<!-- Post Title with Search Highlight -->
												<h3 class="card-title h6 mb-2">
													<a href="<?php the_permalink(); ?>" class="text-decoration-none text-dark">
														<?php
														$title = get_the_title();
														$search_query = get_search_query();
														if ($search_query) {
															$highlighted_title = preg_replace('/(' . preg_quote($search_query, '/') . ')/i', '<mark>$1</mark>', $title);
															echo $highlighted_title;
														} else {
															echo $title;
														}
														?>
													</a>
												</h3>

												<!-- Post Excerpt with Search Highlight -->
												<div class="card-text text-muted small mb-3">
													<?php
													$excerpt = wp_trim_words(get_the_excerpt(), 20);
													if ($search_query) {
														$highlighted_excerpt = preg_replace('/(' . preg_quote($search_query, '/') . ')/i', '<mark>$1</mark>', $excerpt);
														echo $highlighted_excerpt;
													} else {
														echo $excerpt;
													}
													?>
												</div>

												<!-- Post Footer -->
												<div class="d-flex align-items-center justify-content-between">
													<a href="<?php the_permalink(); ?>" class="btn btn-primary btn-sm">
														<?php esc_html_e('Read More', 'technews-pro'); ?>
														<i class="fas fa-arrow-<?php echo is_rtl() ? 'left' : 'right'; ?> ms-1"></i>
													</a>

													<div class="post-actions">
														<?php if (function_exists('technews_pro_reading_time') && 'post' === get_post_type()) : ?>
															<small class="text-muted">
																<?php echo technews_pro_reading_time(); ?>
															</small>
														<?php endif; ?>
													</div>
												</div>
											</div>
										</div>
									</article>
								</div>
							<?php endwhile; ?>
						</div>
					</div>

					<!-- Search Pagination -->
					<nav class="search-pagination mt-5" aria-label="<?php esc_attr_e('Search pagination', 'technews-pro'); ?>">
						<?php
						the_posts_pagination(array(
							'mid_size'  => 2,
							'prev_text' => '<i class="fas fa-chevron-' . (is_rtl() ? 'right' : 'left') . ' me-1"></i>' . esc_html__('Previous', 'technews-pro'),
							'next_text' => esc_html__('Next', 'technews-pro') . '<i class="fas fa-chevron-' . (is_rtl() ? 'left' : 'right') . ' ms-1"></i>',
							'class'     => 'pagination justify-content-center',
						));
						?>
					</nav>

				<?php else : ?>

					<!-- No Search Results -->
					<div class="no-search-results text-center py-5">
						<div class="no-results-content">
							<i class="fas fa-search-minus fa-3x text-muted mb-3"></i>
							<h2 class="h4 mb-3">
								<?php esc_html_e('Nothing Found', 'technews-pro'); ?>
							</h2>
							<p class="text-muted mb-4">
								<?php
								printf(
									/* translators: %s: search query. */
									esc_html__('Sorry, but nothing matched your search terms "%s". Please try again with some different keywords.', 'technews-pro'),
									'<strong>' . get_search_query() . '</strong>'
								);
								?>
							</p>

							<!-- Search Again Form -->
							<div class="search-again-form mb-4">
								<?php get_search_form(); ?>
							</div>

							<!-- Suggested Actions -->
							<div class="suggested-actions">
								<a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-primary me-2">
									<i class="fas fa-home me-1"></i>
									<?php esc_html_e('Go Home', 'technews-pro'); ?>
								</a>
								<a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>" class="btn btn-outline-primary">
									<i class="fas fa-newspaper me-1"></i>
									<?php esc_html_e('Browse All Posts', 'technews-pro'); ?>
								</a>
							</div>
						</div>
					</div>

				<?php endif; ?>

			</main><!-- #main -->
		</div>

		<!-- Sidebar -->
		<div class="col-lg-4 col-md-12">
			<?php get_sidebar(); ?>
		</div>
	</div>
</div>

<?php
get_footer();
