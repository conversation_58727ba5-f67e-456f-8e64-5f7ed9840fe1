<?php
/**
 * Template for displaying search forms in TechNews Pro
 *
 * @package TechNews_Pro
 */

?>

<form role="search" method="get" class="search-form" action="<?php echo esc_url( home_url( '/' ) ); ?>">
	<div class="input-group">
		<input type="search" 
			   class="form-control search-field" 
			   placeholder="<?php echo esc_attr_x( 'Search for tech news...', 'placeholder', 'technews-pro' ); ?>" 
			   value="<?php echo get_search_query(); ?>" 
			   name="s" 
			   aria-label="<?php echo esc_attr_x( 'Search for:', 'label', 'technews-pro' ); ?>" />
		<button type="submit" class="btn btn-primary search-submit">
			<i class="fas fa-search" aria-hidden="true"></i>
			<span class="screen-reader-text"><?php echo esc_html_x( 'Search', 'submit button', 'technews-pro' ); ?></span>
		</button>
	</div>
</form>
