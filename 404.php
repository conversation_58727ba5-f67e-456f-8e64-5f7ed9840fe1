<?php

/**
 * The template for displaying 404 pages (not found)
 *
 * @link https://codex.wordpress.org/Creating_an_Error_404_Page
 *
 * @package TechNews_Pro
 */

get_header();
?>

<div class="container">
	<div class="row justify-content-center">
		<div class="col-lg-8 col-md-10">
			<main id="primary" class="site-main">

				<!-- 404 Error Section -->
				<section class="error-404 not-found text-center py-5">
					<div class="error-content">

						<!-- 404 Illustration -->
						<div class="error-illustration mb-4">
							<div class="error-number display-1 fw-bold text-primary mb-3" style="font-size: 8rem; line-height: 1;">
								404
							</div>
							<div class="error-icon mb-4">
								<i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
							</div>
						</div>

						<!-- Error Message -->
						<header class="page-header mb-4">
							<h1 class="page-title h2 mb-3">
								<?php esc_html_e('Oops! That page can\'t be found.', 'technews-pro'); ?>
							</h1>
							<p class="error-description text-muted lead">
								<?php esc_html_e('It looks like nothing was found at this location. The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.', 'technews-pro'); ?>
							</p>
						</header>

						<!-- Search Form -->
						<div class="error-search mb-5">
							<h3 class="h5 mb-3"><?php esc_html_e('Try searching for what you need:', 'technews-pro'); ?></h3>
							<div class="search-form-container">
								<?php get_search_form(); ?>
							</div>
						</div>

						<!-- Helpful Links -->
						<div class="helpful-links mb-5">
							<h3 class="h5 mb-3"><?php esc_html_e('Here are some helpful links instead:', 'technews-pro'); ?></h3>
							<div class="row">
								<div class="col-md-6 mb-3">
									<div class="helpful-link-card bg-light p-3 rounded h-100">
										<i class="fas fa-home fa-2x text-primary mb-2"></i>
										<h4 class="h6 mb-2"><?php esc_html_e('Go Home', 'technews-pro'); ?></h4>
										<p class="small text-muted mb-3"><?php esc_html_e('Return to our homepage and start fresh.', 'technews-pro'); ?></p>
										<a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-primary btn-sm">
											<i class="fas fa-home me-1"></i>
											<?php esc_html_e('Homepage', 'technews-pro'); ?>
										</a>
									</div>
								</div>
								<div class="col-md-6 mb-3">
									<div class="helpful-link-card bg-light p-3 rounded h-100">
										<i class="fas fa-newspaper fa-2x text-primary mb-2"></i>
										<h4 class="h6 mb-2"><?php esc_html_e('Latest News', 'technews-pro'); ?></h4>
										<p class="small text-muted mb-3"><?php esc_html_e('Check out our latest tech news and articles.', 'technews-pro'); ?></p>
										<a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>" class="btn btn-primary btn-sm">
											<i class="fas fa-newspaper me-1"></i>
											<?php esc_html_e('Browse News', 'technews-pro'); ?>
										</a>
									</div>
								</div>
							</div>
						</div>

						<!-- Popular Categories -->
						<div class="popular-categories mb-5">
							<h3 class="h5 mb-3"><?php esc_html_e('Browse by Category:', 'technews-pro'); ?></h3>
							<div class="categories-grid">
								<?php
								$categories = get_categories(array(
									'orderby' => 'count',
									'order'   => 'DESC',
									'number'  => 6,
									'hide_empty' => true,
								));

								if ($categories) :
								?>
									<div class="row">
										<?php foreach ($categories as $category) : ?>
											<div class="col-lg-4 col-md-6 mb-3">
												<a href="<?php echo esc_url(get_category_link($category->term_id)); ?>"
													class="category-card d-block bg-light p-3 rounded text-decoration-none text-dark h-100">
													<div class="d-flex align-items-center">
														<i class="fas fa-folder text-primary me-2"></i>
														<div>
															<h5 class="h6 mb-1"><?php echo esc_html($category->name); ?></h5>
															<small class="text-muted">
																<?php
																printf(
																	esc_html(_n('%s article', '%s articles', $category->count, 'technews-pro')),
																	number_format_i18n($category->count)
																);
																?>
															</small>
														</div>
													</div>
												</a>
											</div>
										<?php endforeach; ?>
									</div>
								<?php else : ?>
									<p class="text-muted"><?php esc_html_e('No categories found.', 'technews-pro'); ?></p>
								<?php endif; ?>
							</div>
						</div>

						<!-- Contact Information -->
						<div class="contact-info bg-primary text-white p-4 rounded">
							<h3 class="h5 mb-3 text-white"><?php esc_html_e('Still need help?', 'technews-pro'); ?></h3>
							<p class="mb-3"><?php esc_html_e('If you believe this is an error or you need assistance, please don\'t hesitate to contact us.', 'technews-pro'); ?></p>
							<div class="contact-actions">
								<a href="mailto:<?php echo esc_attr(get_option('admin_email')); ?>" class="btn btn-light btn-sm me-2">
									<i class="fas fa-envelope me-1"></i>
									<?php esc_html_e('Contact Us', 'technews-pro'); ?>
								</a>
								<button class="btn btn-outline-light btn-sm" onclick="history.back()">
									<i class="fas fa-arrow-left me-1"></i>
									<?php esc_html_e('Go Back', 'technews-pro'); ?>
								</button>
							</div>
						</div>

					</div><!-- .error-content -->
				</section><!-- .error-404 -->

			</main><!-- #main -->
		</div>
	</div>
</div>

<?php
get_footer();
