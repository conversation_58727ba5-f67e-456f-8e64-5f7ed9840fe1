<?php

/**
 * The template for displaying archive pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package TechNews_Pro
 */

get_header();
?>

<div class="container">
	<div class="row">
		<!-- Main Content -->
		<div class="col-lg-8 col-md-12">
			<main id="primary" class="site-main">

				<?php if (have_posts()) : ?>

					<!-- Archive Header -->
					<header class="page-header mb-5">
						<div class="archive-header-content bg-light p-4 rounded">
							<?php
							the_archive_title('<h1 class="page-title h2 mb-3">', '</h1>');
							the_archive_description('<div class="archive-description text-muted">', '</div>');
							?>

							<!-- Archive Meta -->
							<div class="archive-meta mt-3">
								<div class="d-flex flex-wrap align-items-center gap-3">
									<small class="text-muted">
										<i class="fas fa-newspaper me-1"></i>
										<?php
										global $wp_query;
										$total_posts = $wp_query->found_posts;
										printf(
											esc_html(_n('%s article', '%s articles', $total_posts, 'technews-pro')),
											number_format_i18n($total_posts)
										);
										?>
									</small>

									<?php if (is_category()) : ?>
										<small class="text-muted">
											<i class="fas fa-rss me-1"></i>
											<a href="<?php echo esc_url(get_category_feed_link(get_queried_object_id())); ?>" class="text-decoration-none">
												<?php esc_html_e('RSS Feed', 'technews-pro'); ?>
											</a>
										</small>
									<?php endif; ?>
								</div>
							</div>
						</div>
					</header>

					<!-- Posts Grid -->
					<div class="posts-grid">
						<div class="row">
							<?php
							/* Start the Loop */
							while (have_posts()) :
								the_post();
							?>
								<div class="col-lg-6 col-md-6 mb-4">
									<article id="post-<?php the_ID(); ?>" <?php post_class('archive-post-card h-100'); ?>>
										<div class="card h-100 border-0 shadow-sm">
											<?php if (has_post_thumbnail()) : ?>
												<div class="card-img-top position-relative overflow-hidden">
													<a href="<?php the_permalink(); ?>">
														<?php the_post_thumbnail('technews-grid', array('class' => 'w-100', 'style' => 'height: 200px; object-fit: cover;')); ?>
													</a>

													<!-- Category Badge -->
													<div class="position-absolute top-0 start-0 m-2">
														<?php
														$categories = get_the_category();
														if (! empty($categories)) :
														?>
															<span class="badge bg-primary"><?php echo esc_html($categories[0]->name); ?></span>
														<?php endif; ?>
													</div>
												</div>
											<?php endif; ?>

											<div class="card-body p-3">
												<!-- Post Meta -->
												<div class="post-meta mb-2">
													<small class="text-muted">
														<i class="far fa-calendar-alt me-1"></i>
														<?php echo get_the_date(); ?>
														<span class="mx-2">•</span>
														<i class="far fa-user me-1"></i>
														<?php the_author_posts_link(); ?>
														<span class="mx-2">•</span>
														<i class="far fa-eye me-1"></i>
														<?php echo technews_pro_get_post_views(get_the_ID()); ?>
													</small>
												</div>

												<!-- Post Title -->
												<h3 class="card-title h6 mb-2">
													<a href="<?php the_permalink(); ?>" class="text-decoration-none text-dark">
														<?php the_title(); ?>
													</a>
												</h3>

												<!-- Post Excerpt -->
												<p class="card-text text-muted small mb-3">
													<?php echo wp_trim_words(get_the_excerpt(), 20); ?>
												</p>

												<!-- Post Footer -->
												<div class="d-flex align-items-center justify-content-between">
													<a href="<?php the_permalink(); ?>" class="btn btn-primary btn-sm">
														<?php esc_html_e('Read More', 'technews-pro'); ?>
														<i class="fas fa-arrow-<?php echo is_rtl() ? 'left' : 'right'; ?> ms-1"></i>
													</a>

													<div class="post-actions">
														<?php if (function_exists('technews_pro_reading_time')) : ?>
															<small class="text-muted">
																<?php echo technews_pro_reading_time(); ?>
															</small>
														<?php endif; ?>
													</div>
												</div>
											</div>
										</div>
									</article>
								</div>
							<?php endwhile; ?>
						</div>
					</div>

					<!-- Pagination -->
					<nav class="archive-pagination mt-5" aria-label="<?php esc_attr_e('Archive pagination', 'technews-pro'); ?>">
						<?php
						the_posts_pagination(array(
							'mid_size'  => 2,
							'prev_text' => '<i class="fas fa-chevron-' . (is_rtl() ? 'right' : 'left') . ' me-1"></i>' . esc_html__('Previous', 'technews-pro'),
							'next_text' => esc_html__('Next', 'technews-pro') . '<i class="fas fa-chevron-' . (is_rtl() ? 'left' : 'right') . ' ms-1"></i>',
							'class'     => 'pagination justify-content-center',
						));
						?>
					</nav>

				<?php else : ?>

					<!-- No Posts Found -->
					<div class="no-posts-found text-center py-5">
						<div class="no-posts-content">
							<i class="fas fa-search fa-3x text-muted mb-3"></i>
							<h2 class="h4 mb-3"><?php esc_html_e('Nothing Found', 'technews-pro'); ?></h2>
							<p class="text-muted mb-4">
								<?php
								if (is_category()) {
									esc_html_e('No posts found in this category. Try browsing other categories or use the search function.', 'technews-pro');
								} elseif (is_tag()) {
									esc_html_e('No posts found with this tag. Try browsing other tags or use the search function.', 'technews-pro');
								} elseif (is_author()) {
									esc_html_e('This author has not published any posts yet.', 'technews-pro');
								} elseif (is_date()) {
									esc_html_e('No posts found for this date. Try browsing other dates or use the search function.', 'technews-pro');
								} else {
									esc_html_e('It seems we can\'t find what you\'re looking for. Perhaps searching can help.', 'technews-pro');
								}
								?>
							</p>

							<!-- Search Form -->
							<div class="search-form-container">
								<?php get_search_form(); ?>
							</div>

							<!-- Suggested Actions -->
							<div class="suggested-actions mt-4">
								<a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-primary me-2">
									<i class="fas fa-home me-1"></i>
									<?php esc_html_e('Go Home', 'technews-pro'); ?>
								</a>
								<a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>" class="btn btn-outline-primary">
									<i class="fas fa-newspaper me-1"></i>
									<?php esc_html_e('Browse All Posts', 'technews-pro'); ?>
								</a>
							</div>
						</div>
					</div>

				<?php endif; ?>

			</main><!-- #main -->
		</div>

		<!-- Sidebar -->
		<div class="col-lg-4 col-md-12">
			<?php get_sidebar(); ?>
		</div>
	</div>
</div>

<?php
get_footer();
