<?php
/**
 * Theme Check - Simple diagnostic file for TechNews Pro
 * 
 * This file helps diagnose common issues with the theme
 * Place this file in the theme root and access it via browser
 * 
 * @package TechNews_Pro
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit( 'Direct access not allowed.' );
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechNews Pro - Theme Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
        .file-list { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 TechNews Pro - Theme Diagnostic</h1>
        
        <h2>📋 Basic Information</h2>
        <div class="info">
            <strong>Theme Name:</strong> TechNews Pro<br>
            <strong>Version:</strong> 1.0.0<br>
            <strong>WordPress Version:</strong> <?php echo get_bloginfo( 'version' ); ?><br>
            <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
            <strong>Current Time:</strong> <?php echo date( 'Y-m-d H:i:s' ); ?>
        </div>

        <h2>📁 Required Files Check</h2>
        <?php
        $required_files = array(
            'style.css' => 'Main stylesheet',
            'index.php' => 'Main template file',
            'functions.php' => 'Theme functions',
            'header.php' => 'Header template',
            'footer.php' => 'Footer template',
            'sidebar.php' => 'Sidebar template',
            'single.php' => 'Single post template',
            'archive.php' => 'Archive template',
            'search.php' => 'Search results template',
            '404.php' => '404 error template',
            'page.php' => 'Page template',
            'searchform.php' => 'Search form template'
        );

        $missing_files = array();
        foreach ( $required_files as $file => $description ) {
            if ( file_exists( get_template_directory() . '/' . $file ) ) {
                echo '<div class="status success">✅ ' . $file . ' - ' . $description . '</div>';
            } else {
                echo '<div class="status error">❌ ' . $file . ' - ' . $description . ' (MISSING)</div>';
                $missing_files[] = $file;
            }
        }
        ?>

        <h2>🔧 Functions Check</h2>
        <?php
        $custom_functions = array(
            'technews_pro_setup' => 'Theme setup function',
            'technews_pro_scripts' => 'Scripts enqueue function',
            'technews_pro_widgets_init' => 'Widgets initialization',
            'technews_pro_get_featured_posts' => 'Get featured posts',
            'technews_pro_get_latest_posts' => 'Get latest posts',
            'technews_pro_get_post_views' => 'Get post views',
            'technews_pro_set_post_views' => 'Set post views',
            'technews_pro_reading_time' => 'Calculate reading time',
            'technews_pro_social_share_buttons' => 'Social share buttons'
        );

        foreach ( $custom_functions as $function => $description ) {
            if ( function_exists( $function ) ) {
                echo '<div class="status success">✅ ' . $function . '() - ' . $description . '</div>';
            } else {
                echo '<div class="status error">❌ ' . $function . '() - ' . $description . ' (NOT FOUND)</div>';
            }
        }
        ?>

        <h2>📦 Classes Check</h2>
        <?php
        $custom_classes = array(
            'WP_Bootstrap_Navwalker' => 'Bootstrap Navigation Walker'
        );

        foreach ( $custom_classes as $class => $description ) {
            if ( class_exists( $class ) ) {
                echo '<div class="status success">✅ ' . $class . ' - ' . $description . '</div>';
            } else {
                echo '<div class="status error">❌ ' . $class . ' - ' . $description . ' (NOT FOUND)</div>';
            }
        }
        ?>

        <h2>🎨 Theme Support Check</h2>
        <?php
        $theme_supports = array(
            'post-thumbnails' => 'Featured Images',
            'automatic-feed-links' => 'RSS Feed Links',
            'title-tag' => 'Title Tag Support',
            'custom-logo' => 'Custom Logo',
            'html5' => 'HTML5 Support',
            'customize-selective-refresh-widgets' => 'Widget Selective Refresh',
            'wp-block-styles' => 'Block Styles',
            'align-wide' => 'Wide Alignment',
            'editor-styles' => 'Editor Styles',
            'responsive-embeds' => 'Responsive Embeds'
        );

        foreach ( $theme_supports as $feature => $description ) {
            if ( current_theme_supports( $feature ) ) {
                echo '<div class="status success">✅ ' . $feature . ' - ' . $description . '</div>';
            } else {
                echo '<div class="status warning">⚠️ ' . $feature . ' - ' . $description . ' (NOT ENABLED)</div>';
            }
        }
        ?>

        <h2>📍 Navigation Menus</h2>
        <?php
        $nav_menus = get_registered_nav_menus();
        if ( ! empty( $nav_menus ) ) {
            foreach ( $nav_menus as $location => $description ) {
                echo '<div class="status success">✅ ' . $location . ' - ' . $description . '</div>';
            }
        } else {
            echo '<div class="status error">❌ No navigation menus registered</div>';
        }
        ?>

        <h2>🎛️ Widget Areas</h2>
        <?php
        global $wp_registered_sidebars;
        if ( ! empty( $wp_registered_sidebars ) ) {
            foreach ( $wp_registered_sidebars as $sidebar ) {
                echo '<div class="status success">✅ ' . $sidebar['id'] . ' - ' . $sidebar['name'] . '</div>';
            }
        } else {
            echo '<div class="status error">❌ No widget areas registered</div>';
        }
        ?>

        <h2>🖼️ Image Sizes</h2>
        <?php
        global $_wp_additional_image_sizes;
        $image_sizes = array_merge( get_intermediate_image_sizes(), array_keys( $_wp_additional_image_sizes ) );
        $custom_sizes = array( 'technews-featured', 'technews-slider', 'technews-grid', 'technews-thumbnail' );
        
        foreach ( $custom_sizes as $size ) {
            if ( in_array( $size, $image_sizes ) ) {
                echo '<div class="status success">✅ ' . $size . ' - Custom image size registered</div>';
            } else {
                echo '<div class="status error">❌ ' . $size . ' - Custom image size not found</div>';
            }
        }
        ?>

        <h2>⚠️ Common Issues & Solutions</h2>
        <div class="file-list">
            <h3>If you see "Fatal Error":</h3>
            <ul>
                <li>Check that all required files exist</li>
                <li>Verify PHP syntax in functions.php</li>
                <li>Ensure Bootstrap Navwalker class is loaded</li>
                <li>Check file permissions (should be 644 for files, 755 for directories)</li>
            </ul>

            <h3>If functions are missing:</h3>
            <ul>
                <li>Make sure functions.php is properly loaded</li>
                <li>Check for PHP syntax errors</li>
                <li>Verify function names are spelled correctly</li>
            </ul>

            <h3>If styles are not loading:</h3>
            <ul>
                <li>Check that wp_enqueue_scripts is working</li>
                <li>Verify file paths are correct</li>
                <li>Clear any caching plugins</li>
            </ul>
        </div>

        <div class="info">
            <strong>💡 Tip:</strong> If everything shows green checkmarks but you still have issues, 
            try deactivating all plugins temporarily to check for conflicts.
        </div>

        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 4px; text-align: center;">
            <small>TechNews Pro Theme Diagnostic Tool v1.0 | 
            <a href="<?php echo home_url(); ?>">← Back to Site</a></small>
        </div>
    </div>
</body>
</html>
