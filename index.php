<?php

/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * E.g., it puts together the home page when no home.php file exists.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package TechNews_Pro
 */

get_header();
?>

<div class="container-fluid px-0">
	<?php if (is_home() && is_front_page()) : ?>

		<!-- Featured News Slider -->
		<section class="featured-slider mb-5">
			<div class="container">
				<div class="row">
					<div class="col-12">
						<div id="featuredSlider" class="carousel slide" data-bs-ride="carousel">
							<div class="carousel-indicators">
								<?php
								$featured_posts = technews_pro_get_featured_posts(5);
								$slide_count = 0;
								if ($featured_posts->have_posts()) :
									while ($featured_posts->have_posts()) : $featured_posts->the_post();
								?>
										<button type="button" data-bs-target="#featuredSlider" data-bs-slide-to="<?php echo $slide_count; ?>" <?php echo $slide_count === 0 ? 'class="active" aria-current="true"' : ''; ?> aria-label="Slide <?php echo $slide_count + 1; ?>"></button>
								<?php
										$slide_count++;
									endwhile;
									wp_reset_postdata();
								endif;
								?>
							</div>

							<div class="carousel-inner">
								<?php
								$featured_posts = technews_pro_get_featured_posts(5);
								$slide_count = 0;
								if ($featured_posts->have_posts()) :
									while ($featured_posts->have_posts()) : $featured_posts->the_post();
								?>
										<div class="carousel-item <?php echo $slide_count === 0 ? 'active' : ''; ?>">
											<div class="position-relative">
												<?php if (has_post_thumbnail()) : ?>
													<img src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'technews-slider')); ?>" class="d-block w-100" alt="<?php the_title_attribute(); ?>" style="height: 500px; object-fit: cover;">
												<?php else : ?>
													<div class="bg-secondary d-block w-100" style="height: 500px;"></div>
												<?php endif; ?>

												<div class="carousel-caption d-block">
													<div class="bg-dark bg-opacity-75 p-4 rounded">
														<div class="mb-2">
															<?php
															$categories = get_the_category();
															if (! empty($categories)) :
															?>
																<span class="badge bg-primary me-2"><?php echo esc_html($categories[0]->name); ?></span>
															<?php endif; ?>
															<small class="text-light">
																<i class="far fa-clock me-1"></i>
																<?php echo get_the_date(); ?>
															</small>
														</div>
														<h3 class="h4 text-white mb-3">
															<a href="<?php the_permalink(); ?>" class="text-white text-decoration-none">
																<?php the_title(); ?>
															</a>
														</h3>
														<p class="text-light mb-3"><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
														<a href="<?php the_permalink(); ?>" class="btn btn-primary">
															<?php esc_html_e('Read More', 'technews-pro'); ?>
															<i class="fas fa-arrow-<?php echo is_rtl() ? 'left' : 'right'; ?> ms-1"></i>
														</a>
													</div>
												</div>
											</div>
										</div>
									<?php
										$slide_count++;
									endwhile;
									wp_reset_postdata();
								else :
									?>
									<div class="carousel-item active">
										<div class="bg-secondary d-block w-100 d-flex align-items-center justify-content-center" style="height: 500px;">
											<div class="text-center text-white">
												<h3><?php esc_html_e('No Featured Posts Found', 'technews-pro'); ?></h3>
												<p><?php esc_html_e('Please add some posts with featured images.', 'technews-pro'); ?></p>
											</div>
										</div>
									</div>
								<?php endif; ?>
							</div>

							<button class="carousel-control-prev" type="button" data-bs-target="#featuredSlider" data-bs-slide="prev">
								<span class="carousel-control-prev-icon" aria-hidden="true"></span>
								<span class="visually-hidden"><?php esc_html_e('Previous', 'technews-pro'); ?></span>
							</button>
							<button class="carousel-control-next" type="button" data-bs-target="#featuredSlider" data-bs-slide="next">
								<span class="carousel-control-next-icon" aria-hidden="true"></span>
								<span class="visually-hidden"><?php esc_html_e('Next', 'technews-pro'); ?></span>
							</button>
						</div>
					</div>
				</div>
			</div>
		</section>

	<?php endif; ?>

	<!-- Main Content Area -->
	<div class="container">
		<div class="row">
			<!-- Main Content -->
			<div class="col-lg-8 col-md-12">
				<?php if (is_home() && is_front_page()) : ?>

					<!-- Latest News Section -->
					<section class="latest-news mb-5">
						<div class="section-header d-flex align-items-center justify-content-between mb-4">
							<h2 class="section-title h3 mb-0 position-relative">
								<?php esc_html_e('Latest News', 'technews-pro'); ?>
								<span class="position-absolute bottom-0 start-0 bg-primary" style="height: 3px; width: 50px;"></span>
							</h2>
							<a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>" class="btn btn-outline-primary btn-sm">
								<?php esc_html_e('View All', 'technews-pro'); ?>
								<i class="fas fa-arrow-<?php echo is_rtl() ? 'left' : 'right'; ?> ms-1"></i>
							</a>
						</div>

						<div class="row">
							<?php
							// Get featured post IDs to exclude from latest news
							$featured_posts = technews_pro_get_featured_posts(5);
							$exclude_ids = array();
							if ($featured_posts->have_posts()) :
								while ($featured_posts->have_posts()) : $featured_posts->the_post();
									$exclude_ids[] = get_the_ID();
								endwhile;
								wp_reset_postdata();
							endif;

							// Get latest posts excluding featured ones
							$latest_posts = technews_pro_get_latest_posts(12, $exclude_ids);
							if ($latest_posts->have_posts()) :
								while ($latest_posts->have_posts()) : $latest_posts->the_post();
							?>
									<div class="col-lg-6 col-md-6 mb-4">
										<article class="news-card h-100 border rounded overflow-hidden shadow-sm">
											<?php if (has_post_thumbnail()) : ?>
												<div class="news-card-image position-relative">
													<a href="<?php the_permalink(); ?>">
														<img src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'technews-grid')); ?>" class="card-img-top" alt="<?php the_title_attribute(); ?>" style="height: 200px; object-fit: cover;">
													</a>
													<div class="position-absolute top-0 start-0 m-2">
														<?php
														$categories = get_the_category();
														if (! empty($categories)) :
														?>
															<span class="badge bg-primary"><?php echo esc_html($categories[0]->name); ?></span>
														<?php endif; ?>
													</div>
												</div>
											<?php endif; ?>

											<div class="card-body p-3">
												<div class="news-meta mb-2">
													<small class="text-muted">
														<i class="far fa-clock me-1"></i>
														<?php echo get_the_date(); ?>
														<span class="mx-2">•</span>
														<i class="far fa-user me-1"></i>
														<?php the_author(); ?>
													</small>
												</div>

												<h3 class="news-title h6 mb-2">
													<a href="<?php the_permalink(); ?>" class="text-decoration-none text-dark">
														<?php the_title(); ?>
													</a>
												</h3>

												<p class="news-excerpt text-muted small mb-3">
													<?php echo wp_trim_words(get_the_excerpt(), 15); ?>
												</p>

												<div class="d-flex align-items-center justify-content-between">
													<a href="<?php the_permalink(); ?>" class="btn btn-primary btn-sm">
														<?php esc_html_e('Read More', 'technews-pro'); ?>
													</a>
													<div class="news-stats">
														<small class="text-muted">
															<i class="far fa-eye me-1"></i>
															<?php echo technews_pro_get_post_views(get_the_ID()); ?>
														</small>
													</div>
												</div>
											</div>
										</article>
									</div>
								<?php
								endwhile;
								wp_reset_postdata();
							else :
								?>
								<div class="col-12">
									<div class="text-center py-5">
										<h3><?php esc_html_e('No Posts Found', 'technews-pro'); ?></h3>
										<p class="text-muted"><?php esc_html_e('There are no posts to display at the moment.', 'technews-pro'); ?></p>
									</div>
								</div>
							<?php endif; ?>
						</div>
					</section>

				<?php else : ?>

					<!-- Blog Archive/Category Pages -->
					<main id="primary" class="site-main">
						<?php if (have_posts()) : ?>

							<?php if (is_home() && ! is_front_page()) : ?>
								<header class="page-header mb-4">
									<h1 class="page-title h2"><?php single_post_title(); ?></h1>
								</header>
							<?php endif; ?>

							<div class="row">
								<?php while (have_posts()) : the_post(); ?>
									<div class="col-lg-6 col-md-6 mb-4">
										<?php get_template_part('template-parts/content', 'grid'); ?>
									</div>
								<?php endwhile; ?>
							</div>

							<?php the_posts_navigation(); ?>

						<?php else : ?>
							<?php get_template_part('template-parts/content', 'none'); ?>
						<?php endif; ?>
					</main><!-- #main -->

				<?php endif; ?>
			</div>

			<!-- Sidebar -->
			<div class="col-lg-4 col-md-12">
				<?php get_sidebar(); ?>
			</div>
		</div>
	</div>
</div>

<?php
get_footer();
