/*!
Theme Name: TechNews Pro
Theme URI: https://technews-pro.com/
Author: TechNews Team
Author URI: https://technews-pro.com/
Description: ثيم ووردبريس احترافي وعصري مخصص للمواقع الإخبارية التقنية. يدعم RTL بالكامل، متجاوب، ومتوافق مع محرر Gutenberg ومعايير WordPress 6.8.1. يستخدم Bootstrap 5.3 RTL ويتميز بتصميم حديث بستايل Tech News Magazine.
Version: 1.0.0
Tested up to: 6.8.1
Requires at least: 6.0
Requires PHP: 8.0
License: GNU General Public License v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: technews-pro
Tags: news, magazine, rtl-language-support, responsive-layout, custom-background, custom-logo, custom-menu, featured-images, threaded-comments, translation-ready, block-styles, wide-blocks, editor-style, custom-colors, custom-header, footer-widgets, theme-options, two-columns, three-columns, grid-layout, bootstrap

TechNews Pro - Professional WordPress Theme for Tech News Websites
Supports RTL, fully responsive, Gutenberg compatible, and follows WordPress 6.8.1 standards.
Built with Bootstrap 5.3 RTL and modern Tech News Magazine design.

This theme is licensed under the GPL v2 or later.
Use it to create amazing tech news websites with modern design and functionality.

TechNews Pro uses Bootstrap 5.3 RTL for responsive design and modern UI components.
Normalizing styles have been helped along thanks to the fine work of
Nicolas Gallagher and Jonathan Neal https://necolas.github.io/normalize.css/
*/

/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Generic
	- Normalize
	- Box sizing
# Base
	- Typography
	- Elements
	- Links
	- Forms
## Layouts
# Components
	- Navigation
	- Posts and pages
	- Comments
	- Widgets
	- Media
	- Captions
	- Galleries
# plugins
	- Jetpack infinite scroll
# Utilities
	- Accessibility
	- Alignments

--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Generic
--------------------------------------------------------------*/

/* Normalize
--------------------------------------------- */

/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */

/* Document
	 ========================================================================== */

/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}

/* Sections
	 ========================================================================== */

/**
 * Remove the margin in all browsers.
 */
body {
  margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */
main {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
	 ========================================================================== */

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
  font-family: monospace, monospace;
  font-size: 1em;
}

/* Text-level semantics
	 ========================================================================== */

/**
 * Remove the gray background on active links in IE 10.
 */
a {
  background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  text-decoration: underline dotted;
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

/**
 * Add the correct font size in all browsers.
 */
small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
	 ========================================================================== */

/**
 * Remove the border on images inside links in IE 10.
 */
img {
  border-style: none;
}

/* Forms
	 ========================================================================== */

/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
  text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type='button']:-moz-focusring,
[type='reset']:-moz-focusring,
[type='submit']:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */
fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *		`fieldset` elements in all browsers.
 */
legend {
  box-sizing: border-box;
  color: inherit;
  display: table;
  max-width: 100%;
  padding: 0;
  white-space: normal;
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type='checkbox'],
[type='radio'] {
  box-sizing: border-box;
  padding: 0;
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type='search'] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

/* Interactive
	 ========================================================================== */

/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */
summary {
  display: list-item;
}

/* Misc
	 ========================================================================== */

/**
 * Add the correct display in IE 10+.
 */
template {
  display: none;
}

/**
 * Add the correct display in IE 10.
 */
[hidden] {
  display: none;
}

/* Box sizing
--------------------------------------------- */

/* Inherit box-sizing to more easily change it's value on a component level.
@link http://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/ */
*,
*::before,
*::after {
  box-sizing: inherit;
}

html {
  box-sizing: border-box;
}

/*--------------------------------------------------------------
# Base
--------------------------------------------------------------*/

/* Typography
--------------------------------------------- */
body,
button,
input,
select,
optgroup,
textarea {
  color: #404040;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-size: 1rem;
  line-height: 1.5;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  clear: both;
}

p {
  margin-bottom: 1.5em;
}

dfn,
cite,
em,
i {
  font-style: italic;
}

blockquote {
  margin: 0 1.5em;
}

address {
  margin: 0 0 1.5em;
}

pre {
  background: #eee;
  font-family: 'Courier 10 Pitch', courier, monospace;
  line-height: 1.6;
  margin-bottom: 1.6em;
  max-width: 100%;
  overflow: auto;
  padding: 1.6em;
}

code,
kbd,
tt,
var {
  font-family: monaco, consolas, 'Andale Mono', 'DejaVu Sans Mono', monospace;
}

abbr,
acronym {
  border-bottom: 1px dotted #666;
  cursor: help;
}

mark,
ins {
  background: #fff9c0;
  text-decoration: none;
}

big {
  font-size: 125%;
}

/* Elements
--------------------------------------------- */
body {
  background: #fff;
}

hr {
  background-color: #ccc;
  border: 0;
  height: 1px;
  margin-bottom: 1.5em;
}

ul,
ol {
  margin: 0 0 1.5em 3em;
}

ul {
  list-style: disc;
}

ol {
  list-style: decimal;
}

li > ul,
li > ol {
  margin-bottom: 0;
  margin-left: 1.5em;
}

dt {
  font-weight: 700;
}

dd {
  margin: 0 1.5em 1.5em;
}

/* Make sure embeds and iframes fit their containers. */
embed,
iframe,
object {
  max-width: 100%;
}

img {
  height: auto;
  max-width: 100%;
}

figure {
  margin: 1em 0;
}

table {
  margin: 0 0 1.5em;
  width: 100%;
}

/* Links
--------------------------------------------- */
a {
  color: #4169e1;
}

a:visited {
  color: #800080;
}

a:hover,
a:focus,
a:active {
  color: #191970;
}

a:focus {
  outline: thin dotted;
}

a:hover,
a:active {
  outline: 0;
}

/* Forms
--------------------------------------------- */
button,
input[type='button'],
input[type='reset'],
input[type='submit'] {
  border: 1px solid;
  border-color: #ccc #ccc #bbb;
  border-radius: 3px;
  background: #e6e6e6;
  color: rgba(0, 0, 0, 0.8);
  line-height: 1;
  padding: 0.6em 1em 0.4em;
}

button:hover,
input[type='button']:hover,
input[type='reset']:hover,
input[type='submit']:hover {
  border-color: #ccc #bbb #aaa;
}

button:active,
button:focus,
input[type='button']:active,
input[type='button']:focus,
input[type='reset']:active,
input[type='reset']:focus,
input[type='submit']:active,
input[type='submit']:focus {
  border-color: #aaa #bbb #bbb;
}

input[type='text'],
input[type='email'],
input[type='url'],
input[type='password'],
input[type='search'],
input[type='number'],
input[type='tel'],
input[type='range'],
input[type='date'],
input[type='month'],
input[type='week'],
input[type='time'],
input[type='datetime'],
input[type='datetime-local'],
input[type='color'],
textarea {
  color: #666;
  border: 1px solid #ccc;
  border-radius: 3px;
  padding: 3px;
}

input[type='text']:focus,
input[type='email']:focus,
input[type='url']:focus,
input[type='password']:focus,
input[type='search']:focus,
input[type='number']:focus,
input[type='tel']:focus,
input[type='range']:focus,
input[type='date']:focus,
input[type='month']:focus,
input[type='week']:focus,
input[type='time']:focus,
input[type='datetime']:focus,
input[type='datetime-local']:focus,
input[type='color']:focus,
textarea:focus {
  color: #111;
}

select {
  border: 1px solid #ccc;
}

textarea {
  width: 100%;
}

/*--------------------------------------------------------------
# Layouts
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Components
--------------------------------------------------------------*/

/* Navigation
--------------------------------------------- */
.main-navigation {
  display: block;
  width: 100%;
}

.main-navigation ul {
  display: none;
  list-style: none;
  margin: 0;
  padding-left: 0;
}

.main-navigation ul ul {
  box-shadow: 0 3px 3px rgba(0, 0, 0, 0.2);
  float: left;
  position: absolute;
  top: 100%;
  left: -999em;
  z-index: 99999;
}

.main-navigation ul ul ul {
  left: -999em;
  top: 0;
}

.main-navigation ul ul li:hover > ul,
.main-navigation ul ul li.focus > ul {
  display: block;
  left: auto;
}

.main-navigation ul ul a {
  width: 200px;
}

.main-navigation ul li:hover > ul,
.main-navigation ul li.focus > ul {
  left: auto;
}

.main-navigation li {
  position: relative;
}

.main-navigation a {
  display: block;
  text-decoration: none;
}

/* Small menu. */
.menu-toggle,
.main-navigation.toggled ul {
  display: block;
}

@media screen and (min-width: 37.5em) {
  .menu-toggle {
    display: none;
  }

  .main-navigation ul {
    display: flex;
  }
}

.site-main .comment-navigation,
.site-main .posts-navigation,
.site-main .post-navigation {
  margin: 0 0 1.5em;
}

.comment-navigation .nav-links,
.posts-navigation .nav-links,
.post-navigation .nav-links {
  display: flex;
}

.comment-navigation .nav-previous,
.posts-navigation .nav-previous,
.post-navigation .nav-previous {
  flex: 1 0 50%;
}

.comment-navigation .nav-next,
.posts-navigation .nav-next,
.post-navigation .nav-next {
  text-align: end;
  flex: 1 0 50%;
}

/* Posts and pages
--------------------------------------------- */
.sticky {
  display: block;
}

.post,
.page {
  margin: 0 0 1.5em;
}

.updated:not(.published) {
  display: none;
}

.page-content,
.entry-content,
.entry-summary {
  margin: 1.5em 0 0;
}

.page-links {
  clear: both;
  margin: 0 0 1.5em;
}

/* Comments
--------------------------------------------- */
.comment-content a {
  word-wrap: break-word;
}

.bypostauthor {
  display: block;
}

/* Widgets
--------------------------------------------- */
.widget {
  margin: 0 0 1.5em;
}

.widget select {
  max-width: 100%;
}

/* Media
--------------------------------------------- */
.page-content .wp-smiley,
.entry-content .wp-smiley,
.comment-content .wp-smiley {
  border: none;
  margin-bottom: 0;
  margin-top: 0;
  padding: 0;
}

/* Make sure logo link wraps around logo image. */
.custom-logo-link {
  display: inline-block;
}

/* Captions
--------------------------------------------- */
.wp-caption {
  margin-bottom: 1.5em;
  max-width: 100%;
}

.wp-caption img[class*='wp-image-'] {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.wp-caption .wp-caption-text {
  margin: 0.8075em 0;
}

.wp-caption-text {
  text-align: center;
}

/* Galleries
--------------------------------------------- */
.gallery {
  margin-bottom: 1.5em;
  display: grid;
  grid-gap: 1.5em;
}

.gallery-item {
  display: inline-block;
  text-align: center;
  width: 100%;
}

.gallery-columns-2 {
  grid-template-columns: repeat(2, 1fr);
}

.gallery-columns-3 {
  grid-template-columns: repeat(3, 1fr);
}

.gallery-columns-4 {
  grid-template-columns: repeat(4, 1fr);
}

.gallery-columns-5 {
  grid-template-columns: repeat(5, 1fr);
}

.gallery-columns-6 {
  grid-template-columns: repeat(6, 1fr);
}

.gallery-columns-7 {
  grid-template-columns: repeat(7, 1fr);
}

.gallery-columns-8 {
  grid-template-columns: repeat(8, 1fr);
}

.gallery-columns-9 {
  grid-template-columns: repeat(9, 1fr);
}

.gallery-caption {
  display: block;
}

/*--------------------------------------------------------------
# Plugins
--------------------------------------------------------------*/

/* Jetpack infinite scroll
--------------------------------------------- */

/* Hide the Posts Navigation and the Footer when Infinite Scroll is in use. */
.infinite-scroll .posts-navigation,
.infinite-scroll.neverending .site-footer {
  display: none;
}

/* Re-display the Theme Footer when Infinite Scroll has reached its end. */
.infinity-end.neverending .site-footer {
  display: block;
}

/*--------------------------------------------------------------
# Utilities
--------------------------------------------------------------*/

/* Accessibility
--------------------------------------------- */

/* Text meant only for screen readers. */
.screen-reader-text {
  border: 0;
  clip: rect(1px, 1px, 1px, 1px);
  clip-path: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute !important;
  width: 1px;
  word-wrap: normal !important;
}

.screen-reader-text:focus {
  background-color: #f1f1f1;
  border-radius: 3px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  clip: auto !important;
  clip-path: none;
  color: #21759b;
  display: block;
  font-size: 0.875rem;
  font-weight: 700;
  height: auto;
  left: 5px;
  line-height: normal;
  padding: 15px 23px 14px;
  text-decoration: none;
  top: 5px;
  width: auto;
  z-index: 100000;
}

/* Do not show the outline on the skip link target. */
#primary[tabindex='-1']:focus {
  outline: 0;
}

/* Alignments
--------------------------------------------- */
.alignleft {
  /*rtl:ignore*/
  float: left;

  /*rtl:ignore*/
  margin-right: 1.5em;
  margin-bottom: 1.5em;
}

.alignright {
  /*rtl:ignore*/
  float: right;

  /*rtl:ignore*/
  margin-left: 1.5em;
  margin-bottom: 1.5em;
}

.aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1.5em;
}

/*--------------------------------------------------------------
# TechNews Pro Custom Styles
--------------------------------------------------------------*/

/* CSS Variables for Theme Colors */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --info-color: #17a2b8;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --darker-color: #212529;
  --tech-blue: #0066cc;
  --tech-dark-blue: #004499;
  --tech-gray: #6c757d;
  --tech-light-gray: #e9ecef;
  --tech-accent: #ff6b35;
  --text-color: #333333;
  --text-muted: #6c757d;
  --border-color: #dee2e6;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Typography */
body {
  font-family: 'Tajawal', 'Roboto', -apple-system, BlinkMacSystemFont,
    'Segoe UI', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-color);
  background-color: #ffffff;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Tajawal', 'Roboto', sans-serif;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

/* RTL Support */
[dir='rtl'] {
  text-align: right;
}

[dir='rtl'] .text-start {
  text-align: right !important;
}

[dir='rtl'] .text-end {
  text-align: left !important;
}

[dir='rtl'] .me-1 {
  margin-left: 0.25rem !important;
  margin-right: 0 !important;
}
[dir='rtl'] .me-2 {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}
[dir='rtl'] .me-3 {
  margin-left: 1rem !important;
  margin-right: 0 !important;
}
[dir='rtl'] .ms-1 {
  margin-right: 0.25rem !important;
  margin-left: 0 !important;
}
[dir='rtl'] .ms-2 {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}

/* Skip Link */
.skip-link {
  position: absolute;
  left: -9999px;
  top: 0;
  z-index: 999999;
  padding: 8px 16px;
  background: var(--primary-color);
  color: white;
  text-decoration: none;
  border-radius: 0 0 4px 4px;
}

.skip-link:focus {
  left: 6px;
}

/* Header Styles */
.top-bar {
  font-size: 0.875rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.social-menu {
  gap: 1rem;
}

.social-menu a {
  font-size: 1.1rem;
  transition: color 0.3s ease;
}

.social-menu a:hover {
  color: var(--primary-color) !important;
}

.site-header {
  transition: box-shadow 0.3s ease;
}

.site-header.scrolled {
  box-shadow: 0 2px 10px var(--shadow-color);
}

.site-branding .custom-logo {
  max-height: 60px;
  width: auto;
}

.header-ad-placeholder {
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Navigation Styles */
.main-navigation {
  box-shadow: 0 2px 4px var(--shadow-color);
}

.navbar-nav .nav-link {
  font-weight: 500;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

/* Breaking News Ticker */
.breaking-news {
  max-width: 300px;
  overflow: hidden;
}

.news-ticker {
  white-space: nowrap;
  animation: ticker 30s linear infinite;
}

@keyframes ticker {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

[dir='rtl'] @keyframes ticker {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Featured Slider */
.featured-slider .carousel-item {
  position: relative;
}

.featured-slider .carousel-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2rem;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
}

.featured-slider .carousel-indicators {
  bottom: 1rem;
}

.featured-slider .carousel-indicators button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 0 4px;
}

/* News Cards */
.news-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid var(--border-color);
}

.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px var(--shadow-color);
}

.news-card-image {
  overflow: hidden;
}

.news-card-image img {
  transition: transform 0.3s ease;
}

.news-card:hover .news-card-image img {
  transform: scale(1.05);
}

.news-title a {
  transition: color 0.3s ease;
}

.news-title a:hover {
  color: var(--primary-color) !important;
}

/* Section Headers */
.section-title {
  position: relative;
  padding-bottom: 0.5rem;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: var(--primary-color);
}

[dir='rtl'] .section-title::after {
  right: 0;
  left: auto;
}

/* Single Post Styles */
.single-post-article {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.entry-header {
  padding: 2rem 0;
}

.entry-content {
  font-size: 1.1rem;
  line-height: 1.8;
}

.entry-content h2,
.entry-content h3,
.entry-content h4 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

.entry-content p {
  margin-bottom: 1.5rem;
}

.entry-content img {
  border-radius: 8px;
  box-shadow: 0 4px 15px var(--shadow-color);
}

/* Social Share Buttons */
.social-share-buttons .btn {
  margin: 0.25rem;
  border-radius: 25px;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.social-share-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px var(--shadow-color);
}

/* Author Bio */
.author-bio {
  border-left: 4px solid var(--primary-color);
  background: var(--light-color);
}

[dir='rtl'] .author-bio {
  border-left: none;
  border-right: 4px solid var(--primary-color);
}

/* Post Navigation */
.post-navigation .card {
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.post-navigation .card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px var(--shadow-color);
  border-color: var(--primary-color);
}

/* Related Posts */
.related-post-card .card {
  transition: all 0.3s ease;
}

.related-post-card .card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px var(--shadow-color);
}

/* Comments Section */
.comments-section .card-header {
  border-bottom: 2px solid var(--primary-color);
}

/* Footer Styles */
.site-footer {
  background: linear-gradient(
    135deg,
    var(--dark-color) 0%,
    var(--darker-color) 100%
  );
}

.footer-bottom {
  background-color: var(--darker-color);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-widget h4 {
  position: relative;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.footer-widget h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 2px;
  background: var(--primary-color);
}

[dir='rtl'] .footer-widget h4::after {
  right: 0;
  left: auto;
}

.footer-widget ul li {
  transition: all 0.3s ease;
}

.footer-widget ul li:hover {
  padding-left: 0.5rem;
}

[dir='rtl'] .footer-widget ul li:hover {
  padding-left: 0;
  padding-right: 0.5rem;
}

.recent-post-item {
  transition: all 0.3s ease;
  padding: 0.5rem;
  border-radius: 4px;
}

.recent-post-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Back to Top Button */
#backToTop {
  width: 50px;
  height: 50px;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px var(--shadow-color);
}

#backToTop:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px var(--shadow-color);
}

/* Sidebar Widgets */
.widget {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px var(--shadow-color);
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.widget:hover {
  box-shadow: 0 4px 20px var(--shadow-color);
}

.widget-title {
  position: relative;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.widget ul {
  list-style: none;
  padding: 0;
}

.widget ul li {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.widget ul li:last-child {
  border-bottom: none;
}

.widget ul li:hover {
  padding-left: 0.5rem;
  background-color: var(--light-color);
}

[dir='rtl'] .widget ul li:hover {
  padding-left: 0;
  padding-right: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .featured-slider .carousel-caption {
    padding: 1rem;
  }

  .featured-slider .carousel-caption h3 {
    font-size: 1.25rem;
  }

  .news-card {
    margin-bottom: 1rem;
  }

  .entry-header {
    padding: 1rem 0;
  }

  .entry-content {
    font-size: 1rem;
  }

  .social-share-buttons .btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
}

@media (max-width: 576px) {
  .top-bar {
    display: none !important;
  }

  .site-branding .site-title {
    font-size: 1.5rem;
  }

  .featured-slider .carousel-caption {
    padding: 0.5rem;
  }

  .featured-slider .carousel-caption h3 {
    font-size: 1.1rem;
  }

  .featured-slider .carousel-caption p {
    display: none;
  }

  .breaking-news {
    display: none !important;
  }
}

/* Print Styles */
@media print {
  .top-bar,
  .main-navigation,
  .social-share-buttons,
  .post-navigation,
  .related-posts,
  .comments-section,
  .site-footer,
  #backToTop {
    display: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .entry-content {
    font-size: 11pt;
  }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #e9ecef;
    --border-color: #495057;
    --shadow-color: rgba(255, 255, 255, 0.1);
  }

  body {
    background-color: var(--dark-color);
    color: var(--text-color);
  }

  .news-card,
  .widget {
    background-color: var(--darker-color);
    border-color: var(--border-color);
  }
}

/* Accessibility Improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus Styles */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #0000ff;
    --text-color: #000000;
    --border-color: #000000;
  }

  .news-card,
  .widget {
    border: 2px solid var(--border-color);
  }
}
