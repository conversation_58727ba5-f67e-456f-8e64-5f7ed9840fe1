<?php

/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package TechNews_Pro
 */

get_header();
?>

<div class="container">
	<div class="row">
		<!-- Main Content -->
		<div class="col-lg-8 col-md-12">
			<main id="primary" class="site-main">

				<?php while (have_posts()) : the_post(); ?>

					<article id="post-<?php the_ID(); ?>" <?php post_class('page-article'); ?>>

						<!-- Page Header -->
						<header class="entry-header mb-4">
							<!-- Breadcrumb -->
							<nav aria-label="breadcrumb" class="mb-3">
								<ol class="breadcrumb">
									<li class="breadcrumb-item">
										<a href="<?php echo esc_url(home_url('/')); ?>" class="text-decoration-none">
											<i class="fas fa-home me-1"></i>
											<?php esc_html_e('Home', 'technews-pro'); ?>
										</a>
									</li>

									<?php
									// Get parent pages for breadcrumb
									$ancestors = get_post_ancestors(get_the_ID());
									$ancestors = array_reverse($ancestors);

									foreach ($ancestors as $ancestor) :
									?>
										<li class="breadcrumb-item">
											<a href="<?php echo esc_url(get_permalink($ancestor)); ?>" class="text-decoration-none">
												<?php echo esc_html(get_the_title($ancestor)); ?>
											</a>
										</li>
									<?php endforeach; ?>

									<li class="breadcrumb-item active" aria-current="page">
										<?php echo wp_trim_words(get_the_title(), 5); ?>
									</li>
								</ol>
							</nav>

							<!-- Page Title -->
							<?php the_title('<h1 class="entry-title h2 mb-4">', '</h1>'); ?>

							<!-- Page Meta -->
							<div class="page-meta mb-4">
								<div class="d-flex flex-wrap align-items-center gap-3">
									<small class="text-muted">
										<i class="far fa-calendar-alt me-1"></i>
										<?php esc_html_e('Last updated:', 'technews-pro'); ?>
										<?php echo get_the_modified_date(); ?>
									</small>

									<?php if (get_the_author()) : ?>
										<small class="text-muted">
											<i class="far fa-user me-1"></i>
											<?php esc_html_e('By:', 'technews-pro'); ?>
											<?php the_author_posts_link(); ?>
										</small>
									<?php endif; ?>

									<?php if (comments_open() || get_comments_number()) : ?>
										<small class="text-muted">
											<i class="far fa-comments me-1"></i>
											<a href="#comments" class="text-decoration-none">
												<?php
												$comments_number = get_comments_number();
												if ($comments_number == 0) {
													esc_html_e('No comments', 'technews-pro');
												} elseif ($comments_number == 1) {
													esc_html_e('1 comment', 'technews-pro');
												} else {
													printf(
														esc_html(_n('%s comment', '%s comments', $comments_number, 'technews-pro')),
														number_format_i18n($comments_number)
													);
												}
												?>
											</a>
										</small>
									<?php endif; ?>
								</div>
							</div>

							<!-- Featured Image -->
							<?php if (has_post_thumbnail()) : ?>
								<div class="page-thumbnail mb-4">
									<figure class="figure w-100">
										<?php the_post_thumbnail('technews-featured', array('class' => 'figure-img img-fluid rounded')); ?>
										<?php
										$caption = get_the_post_thumbnail_caption();
										if ($caption) :
										?>
											<figcaption class="figure-caption text-center mt-2"><?php echo esc_html($caption); ?></figcaption>
										<?php endif; ?>
									</figure>
								</div>
							<?php endif; ?>
						</header>

						<!-- Page Content -->
						<div class="entry-content">
							<?php
							the_content();

							wp_link_pages(
								array(
									'before' => '<div class="page-links mt-4"><span class="page-links-title">' . esc_html__('Pages:', 'technews-pro') . '</span>',
									'after'  => '</div>',
									'link_before' => '<span class="page-number">',
									'link_after'  => '</span>',
								)
							);
							?>
						</div>

						<!-- Page Footer -->
						<footer class="entry-footer mt-5">
							<?php
							// Show edit link for logged in users
							if (current_user_can('edit_post', get_the_ID())) :
							?>
								<div class="edit-link mb-3">
									<a href="<?php echo esc_url(get_edit_post_link()); ?>" class="btn btn-outline-secondary btn-sm">
										<i class="fas fa-edit me-1"></i>
										<?php esc_html_e('Edit Page', 'technews-pro'); ?>
									</a>
								</div>
							<?php endif; ?>

							<!-- Social Share Buttons for Pages -->
							<?php if (function_exists('technews_pro_social_share_buttons')) : ?>
								<div class="page-share">
									<?php technews_pro_social_share_buttons(); ?>
								</div>
							<?php endif; ?>
						</footer>
					</article>

					<!-- Comments -->
					<?php
					if (comments_open() || get_comments_number()) :
					?>
						<section class="page-comments mt-5">
							<div class="card">
								<div class="card-header bg-light">
									<h3 class="h5 mb-0">
										<i class="far fa-comments me-2"></i>
										<?php esc_html_e('Comments', 'technews-pro'); ?>
									</h3>
								</div>
								<div class="card-body">
									<?php comments_template(); ?>
								</div>
							</div>
						</section>
					<?php endif; ?>

				<?php endwhile; ?>

			</main><!-- #main -->
		</div>

		<!-- Sidebar -->
		<div class="col-lg-4 col-md-12">
			<?php get_sidebar(); ?>
		</div>
	</div>
</div>

<?php
get_footer();
