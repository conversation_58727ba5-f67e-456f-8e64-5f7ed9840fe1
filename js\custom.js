/**
 * TechNews Pro Custom JavaScript
 * 
 * @package TechNews_Pro
 */

(function($) {
    'use strict';

    // Document Ready
    $(document).ready(function() {
        
        // Initialize all functions
        initHeaderScroll();
        initSearchToggle();
        initNewsSlider();
        initBackToTop();
        initSmoothScroll();
        initLazyLoading();
        initTooltips();
        initReadingProgress();
        initMobileMenu();
        
    });

    /**
     * Header scroll effect
     */
    function initHeaderScroll() {
        $(window).scroll(function() {
            var scroll = $(window).scrollTop();
            
            if (scroll >= 100) {
                $('.site-header').addClass('scrolled');
            } else {
                $('.site-header').removeClass('scrolled');
            }
        });
    }

    /**
     * Search toggle functionality
     */
    function initSearchToggle() {
        $('.search-toggle').on('click', function(e) {
            e.preventDefault();
            $('#searchCollapse').collapse('toggle');
            
            setTimeout(function() {
                $('#searchCollapse input[type="search"]').focus();
            }, 300);
        });
    }

    /**
     * News slider enhancements
     */
    function initNewsSlider() {
        // Auto-pause on hover
        $('#featuredSlider').hover(
            function() {
                $(this).carousel('pause');
            },
            function() {
                $(this).carousel('cycle');
            }
        );

        // Keyboard navigation
        $(document).keydown(function(e) {
            if ($('#featuredSlider').length) {
                if (e.keyCode === 37) { // Left arrow
                    $('#featuredSlider').carousel('prev');
                } else if (e.keyCode === 39) { // Right arrow
                    $('#featuredSlider').carousel('next');
                }
            }
        });
    }

    /**
     * Back to top button
     */
    function initBackToTop() {
        var backToTopButton = $('#backToTop');
        
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                backToTopButton.fadeIn();
            } else {
                backToTopButton.fadeOut();
            }
        });

        backToTopButton.on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: 0
            }, 800);
        });
    }

    /**
     * Smooth scrolling for anchor links
     */
    function initSmoothScroll() {
        $('a[href*="#"]:not([href="#"])').on('click', function() {
            if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && 
                location.hostname === this.hostname) {
                
                var target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 800);
                    return false;
                }
            }
        });
    }

    /**
     * Lazy loading for images
     */
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Initialize Bootstrap tooltips
     */
    function initTooltips() {
        if (typeof bootstrap !== 'undefined') {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    /**
     * Reading progress indicator
     */
    function initReadingProgress() {
        if ($('body').hasClass('single-post')) {
            var progressBar = $('<div class="reading-progress"></div>');
            $('body').append(progressBar);
            
            $(window).scroll(function() {
                var scroll = $(window).scrollTop();
                var height = $(document).height() - $(window).height();
                var progress = (scroll / height) * 100;
                
                progressBar.css('width', progress + '%');
            });
        }
    }

    /**
     * Mobile menu enhancements
     */
    function initMobileMenu() {
        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.navbar').length) {
                $('.navbar-collapse').collapse('hide');
            }
        });

        // Close mobile menu when clicking on a link
        $('.navbar-nav a').on('click', function() {
            $('.navbar-collapse').collapse('hide');
        });
    }

    /**
     * News ticker animation
     */
    function initNewsTicker() {
        $('.news-ticker').each(function() {
            var ticker = $(this);
            var items = ticker.find('.ticker-item');
            var currentIndex = 0;
            
            if (items.length > 1) {
                setInterval(function() {
                    items.eq(currentIndex).fadeOut(500, function() {
                        currentIndex = (currentIndex + 1) % items.length;
                        items.eq(currentIndex).fadeIn(500);
                    });
                }, 5000);
            }
        });
    }

    /**
     * Social share functionality
     */
    function initSocialShare() {
        $('.social-share-buttons a').on('click', function(e) {
            e.preventDefault();
            
            var url = $(this).attr('href');
            var width = 600;
            var height = 400;
            var left = (screen.width / 2) - (width / 2);
            var top = (screen.height / 2) - (height / 2);
            
            window.open(url, 'share', 
                'width=' + width + ',height=' + height + ',left=' + left + ',top=' + top + 
                ',toolbar=0,status=0,resizable=1,scrollbars=1'
            );
        });
    }

    /**
     * Search form enhancements
     */
    function initSearchEnhancements() {
        var searchForm = $('.search-form');
        var searchInput = searchForm.find('input[type="search"]');
        
        // Add search suggestions (if needed)
        searchInput.on('input', function() {
            var query = $(this).val();
            
            if (query.length >= 3) {
                // Implement search suggestions here
                // This would typically involve AJAX calls to get suggestions
            }
        });
    }

    /**
     * Initialize all social and interactive features
     */
    function initInteractiveFeatures() {
        initNewsTicker();
        initSocialShare();
        initSearchEnhancements();
    }

    // Initialize interactive features after DOM is ready
    $(document).ready(function() {
        initInteractiveFeatures();
    });

    /**
     * Handle window resize events
     */
    $(window).resize(function() {
        // Adjust layouts on resize if needed
        if ($(window).width() > 768) {
            $('.navbar-collapse').removeClass('show');
        }
    });

    /**
     * Print functionality
     */
    function initPrintStyles() {
        $('.print-button').on('click', function(e) {
            e.preventDefault();
            window.print();
        });
    }

    // Initialize print functionality
    $(document).ready(function() {
        initPrintStyles();
    });

})(jQuery);

/**
 * Vanilla JavaScript functions (no jQuery dependency)
 */

// Service Worker registration for PWA features (optional)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        // Uncomment if you want to add PWA features
        // navigator.serviceWorker.register('/sw.js');
    });
}

// Performance monitoring
window.addEventListener('load', function() {
    // Log page load time for performance monitoring
    if (window.performance && window.performance.timing) {
        var loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
        console.log('Page load time: ' + loadTime + 'ms');
    }
});

// Error handling
window.addEventListener('error', function(e) {
    // Log JavaScript errors for debugging
    console.error('JavaScript error:', e.error);
});
