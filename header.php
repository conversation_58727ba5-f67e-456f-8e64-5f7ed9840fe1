<?php

/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package TechNews_Pro
 */

?>
<!doctype html>
<html <?php language_attributes(); ?> dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">

<head>
	<meta charset="<?php bloginfo('charset'); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<link rel="profile" href="https://gmpg.org/xfn/11">
	<meta name="description" content="<?php echo esc_attr(get_bloginfo('description')); ?>">

	<?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
	<?php wp_body_open(); ?>

	<!-- Skip Link -->
	<a class="skip-link screen-reader-text" href="#main"><?php esc_html_e('Skip to content', 'technews-pro'); ?></a>

	<div id="page" class="site">
		<!-- Top Bar -->
		<div class="top-bar bg-dark text-white py-2 d-none d-md-block">
			<div class="container">
				<div class="row align-items-center">
					<div class="col-md-6">
						<div class="top-bar-left">
							<span class="me-3">
								<i class="far fa-calendar-alt me-1"></i>
								<?php echo date_i18n(get_option('date_format')); ?>
							</span>
							<span class="me-3">
								<i class="fas fa-thermometer-half me-1"></i>
								<?php esc_html_e('Breaking News', 'technews-pro'); ?>
							</span>
						</div>
					</div>
					<div class="col-md-6">
						<div class="top-bar-right text-end">
							<!-- Social Media Menu -->
							<?php
							wp_nav_menu(array(
								'theme_location' => 'social',
								'menu_class'     => 'social-menu list-unstyled d-inline-flex mb-0',
								'container'      => false,
								'depth'          => 1,
								'link_before'    => '<i class="',
								'link_after'     => '"></i>',
								'fallback_cb'    => false,
							));
							?>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Main Header -->
		<header id="masthead" class="site-header bg-white shadow-sm">
			<div class="container">
				<div class="row align-items-center py-3">
					<!-- Logo Section -->
					<div class="col-lg-3 col-md-4 col-6">
						<div class="site-branding">
							<?php
							if (has_custom_logo()) {
								the_custom_logo();
							} else {
								if (is_front_page() && is_home()) :
							?>
									<h1 class="site-title h3 mb-0">
										<a href="<?php echo esc_url(home_url('/')); ?>" rel="home" class="text-decoration-none text-primary fw-bold">
											<?php bloginfo('name'); ?>
										</a>
									</h1>
								<?php
								else :
								?>
									<p class="site-title h3 mb-0">
										<a href="<?php echo esc_url(home_url('/')); ?>" rel="home" class="text-decoration-none text-primary fw-bold">
											<?php bloginfo('name'); ?>
										</a>
									</p>
								<?php
								endif;

								$technews_description = get_bloginfo('description', 'display');
								if ($technews_description || is_customize_preview()) :
								?>
									<p class="site-description small text-muted mb-0"><?php echo $technews_description; ?></p>
							<?php endif;
							}
							?>
						</div><!-- .site-branding -->
					</div>

					<!-- Header Widget Area -->
					<div class="col-lg-6 col-md-4 d-none d-md-block">
						<div class="header-widget-area text-center">
							<?php if (is_active_sidebar('header-widget')) : ?>
								<?php dynamic_sidebar('header-widget'); ?>
							<?php else : ?>
								<div class="header-ad-placeholder bg-light p-3 rounded">
									<small class="text-muted"><?php esc_html_e('Header Advertisement Space', 'technews-pro'); ?></small>
								</div>
							<?php endif; ?>
						</div>
					</div>

					<!-- Search and Mobile Menu -->
					<div class="col-lg-3 col-md-4 col-6">
						<div class="header-actions d-flex align-items-center justify-content-end">
							<!-- Search Button -->
							<button class="btn btn-outline-primary btn-sm me-2" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="false" aria-controls="searchCollapse">
								<i class="fas fa-search"></i>
								<span class="d-none d-lg-inline ms-1"><?php esc_html_e('Search', 'technews-pro'); ?></span>
							</button>

							<!-- Mobile Menu Toggle -->
							<button class="navbar-toggler btn btn-primary d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="<?php esc_attr_e('Toggle navigation', 'technews-pro'); ?>">
								<i class="fas fa-bars"></i>
							</button>
						</div>
					</div>
				</div>

				<!-- Search Collapse -->
				<div class="collapse" id="searchCollapse">
					<div class="row">
						<div class="col-12">
							<div class="search-form-container bg-light p-3 rounded mb-3">
								<?php get_search_form(); ?>
							</div>
						</div>
					</div>
				</div>
			</div>
		</header>

		<!-- Navigation Menu -->
		<nav id="site-navigation" class="main-navigation navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
			<div class="container">
				<div class="collapse navbar-collapse" id="navbarNav">
					<?php
					wp_nav_menu(array(
						'theme_location'  => 'primary',
						'menu_id'         => 'primary-menu',
						'menu_class'      => 'navbar-nav me-auto',
						'container'       => false,
						'depth'           => 2,
						'walker'          => new WP_Bootstrap_Navwalker(),
						'fallback_cb'     => 'WP_Bootstrap_Navwalker::fallback',
					));
					?>

					<!-- Breaking News Ticker -->
					<div class="breaking-news d-none d-lg-block">
						<div class="d-flex align-items-center">
							<span class="badge bg-danger me-2"><?php esc_html_e('Breaking', 'technews-pro'); ?></span>
							<div class="news-ticker">
								<?php
								$breaking_posts = get_posts(array(
									'numberposts' => 3,
									'meta_key'    => 'breaking_news',
									'meta_value'  => '1'
								));

								if ($breaking_posts) :
									foreach ($breaking_posts as $post) :
										setup_postdata($post);
								?>
										<span class="ticker-item">
											<a href="<?php the_permalink(); ?>" class="text-white text-decoration-none">
												<?php the_title(); ?>
											</a>
										</span>
									<?php
									endforeach;
									wp_reset_postdata();
								else :
									?>
									<span class="ticker-item">
										<span class="text-white"><?php esc_html_e('Stay tuned for breaking news updates', 'technews-pro'); ?></span>
									</span>
								<?php endif; ?>
							</div>
						</div>
					</div>
				</div>
			</div>
		</nav><!-- #site-navigation -->

		<!-- Main Content Area -->
		<main id="main" class="site-main"><?php // Content will be added by other template files 
											?>