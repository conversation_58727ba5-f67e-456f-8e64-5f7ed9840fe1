<?php

/**
 * The sidebar containing the main widget area
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package TechNews_Pro
 */

if (! is_active_sidebar('sidebar-1')) {
	return;
}
?>

<aside id="secondary" class="widget-area">
	<div class="sidebar-content">
		<?php dynamic_sidebar('sidebar-1'); ?>

		<?php if (! is_active_sidebar('sidebar-1')) : ?>
			<!-- Default Sidebar Content -->

			<!-- Search Widget -->
			<div class="widget">
				<h3 class="widget-title h5 mb-3 pb-2 border-bottom border-primary">
					<?php esc_html_e('Search', 'technews-pro'); ?>
				</h3>
				<?php get_search_form(); ?>
			</div>

			<!-- Recent Posts Widget -->
			<div class="widget">
				<h3 class="widget-title h5 mb-3 pb-2 border-bottom border-primary">
					<?php esc_html_e('Recent Posts', 'technews-pro'); ?>
				</h3>
				<div class="recent-posts-widget">
					<?php
					$recent_posts = wp_get_recent_posts(array(
						'numberposts' => 5,
						'post_status' => 'publish'
					));

					if ($recent_posts) :
						foreach ($recent_posts as $post) :
					?>
							<div class="recent-post-item d-flex mb-3 pb-3 border-bottom">
								<?php if (has_post_thumbnail($post['ID'])) : ?>
									<div class="recent-post-thumb me-3">
										<a href="<?php echo esc_url(get_permalink($post['ID'])); ?>">
											<img src="<?php echo esc_url(get_the_post_thumbnail_url($post['ID'], 'technews-thumbnail')); ?>"
												alt="<?php echo esc_attr($post['post_title']); ?>"
												class="rounded"
												style="width: 80px; height: 60px; object-fit: cover;">
										</a>
									</div>
								<?php endif; ?>
								<div class="recent-post-content">
									<h6 class="recent-post-title mb-1">
										<a href="<?php echo esc_url(get_permalink($post['ID'])); ?>" class="text-decoration-none text-dark">
											<?php echo esc_html(wp_trim_words($post['post_title'], 8)); ?>
										</a>
									</h6>
									<small class="text-muted">
										<i class="far fa-calendar-alt me-1"></i>
										<?php echo esc_html(get_the_date('', $post['ID'])); ?>
									</small>
								</div>
							</div>
					<?php
						endforeach;
					endif;
					?>
				</div>
			</div>

			<!-- Categories Widget -->
			<div class="widget">
				<h3 class="widget-title h5 mb-3 pb-2 border-bottom border-primary">
					<?php esc_html_e('Categories', 'technews-pro'); ?>
				</h3>
				<ul class="list-unstyled categories-list">
					<?php
					$categories = get_categories(array(
						'orderby' => 'count',
						'order'   => 'DESC',
						'number'  => 8,
					));

					foreach ($categories as $category) :
					?>
						<li class="mb-2">
							<a href="<?php echo esc_url(get_category_link($category->term_id)); ?>"
								class="d-flex justify-content-between align-items-center text-decoration-none text-dark">
								<span>
									<i class="fas fa-folder me-2 text-primary"></i>
									<?php echo esc_html($category->name); ?>
								</span>
								<span class="badge bg-secondary"><?php echo esc_html($category->count); ?></span>
							</a>
						</li>
					<?php endforeach; ?>
				</ul>
			</div>

			<!-- Popular Posts Widget -->
			<div class="widget">
				<h3 class="widget-title h5 mb-3 pb-2 border-bottom border-primary">
					<?php esc_html_e('Popular Posts', 'technews-pro'); ?>
				</h3>
				<div class="popular-posts-widget">
					<?php
					$popular_posts = get_posts(array(
						'numberposts' => 5,
						'meta_key'    => 'post_views_count',
						'orderby'     => 'meta_value_num',
						'order'       => 'DESC'
					));

					if ($popular_posts) :
						foreach ($popular_posts as $post) : setup_postdata($post);
					?>
							<div class="popular-post-item d-flex mb-3 pb-3 border-bottom">
								<?php if (has_post_thumbnail()) : ?>
									<div class="popular-post-thumb me-3">
										<a href="<?php the_permalink(); ?>">
											<img src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'technews-thumbnail')); ?>"
												alt="<?php the_title_attribute(); ?>"
												class="rounded"
												style="width: 80px; height: 60px; object-fit: cover;">
										</a>
									</div>
								<?php endif; ?>
								<div class="popular-post-content">
									<h6 class="popular-post-title mb-1">
										<a href="<?php the_permalink(); ?>" class="text-decoration-none text-dark">
											<?php echo wp_trim_words(get_the_title(), 8); ?>
										</a>
									</h6>
									<small class="text-muted">
										<i class="far fa-eye me-1"></i>
										<?php echo technews_pro_get_post_views(get_the_ID()); ?> <?php esc_html_e('views', 'technews-pro'); ?>
									</small>
								</div>
							</div>
						<?php
						endforeach;
						wp_reset_postdata();
					else :
						?>
						<p class="text-muted"><?php esc_html_e('No popular posts found.', 'technews-pro'); ?></p>
					<?php endif; ?>
				</div>
			</div>

			<!-- Tags Widget -->
			<div class="widget">
				<h3 class="widget-title h5 mb-3 pb-2 border-bottom border-primary">
					<?php esc_html_e('Tags', 'technews-pro'); ?>
				</h3>
				<div class="tags-cloud">
					<?php
					$tags = get_tags(array(
						'orderby' => 'count',
						'order'   => 'DESC',
						'number'  => 20,
					));

					if ($tags) :
						foreach ($tags as $tag) :
					?>
							<a href="<?php echo esc_url(get_tag_link($tag->term_id)); ?>"
								class="badge bg-light text-dark text-decoration-none me-2 mb-2 d-inline-block">
								<?php echo esc_html($tag->name); ?>
								<span class="text-muted">(<?php echo esc_html($tag->count); ?>)</span>
							</a>
						<?php
						endforeach;
					else :
						?>
						<p class="text-muted"><?php esc_html_e('No tags found.', 'technews-pro'); ?></p>
					<?php endif; ?>
				</div>
			</div>

			<!-- Newsletter Widget -->
			<div class="widget bg-primary text-white">
				<h3 class="widget-title h5 mb-3 text-white">
					<?php esc_html_e('Newsletter', 'technews-pro'); ?>
				</h3>
				<p class="mb-3"><?php esc_html_e('Subscribe to our newsletter to get the latest tech news and updates.', 'technews-pro'); ?></p>
				<form class="newsletter-form">
					<div class="mb-3">
						<input type="email" class="form-control" placeholder="<?php esc_attr_e('Enter your email', 'technews-pro'); ?>" required>
					</div>
					<button type="submit" class="btn btn-light btn-sm w-100">
						<?php esc_html_e('Subscribe', 'technews-pro'); ?>
						<i class="fas fa-paper-plane ms-1"></i>
					</button>
				</form>
			</div>

		<?php endif; ?>
	</div>
</aside><!-- #secondary -->