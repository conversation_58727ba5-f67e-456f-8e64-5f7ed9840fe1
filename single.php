<?php

/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package TechNews_Pro
 */

get_header();

// Set post views
if (function_exists('technews_pro_set_post_views')) {
	technews_pro_set_post_views(get_the_ID());
}
?>

<div class="container">
	<div class="row">
		<!-- Main Content -->
		<div class="col-lg-8 col-md-12">
			<main id="primary" class="site-main">
				<?php while (have_posts()) : the_post(); ?>

					<article id="post-<?php the_ID(); ?>" <?php post_class('single-post-article'); ?>>

						<!-- Post Header -->
						<header class="entry-header mb-4">
							<!-- Breadcrumb -->
							<nav aria-label="breadcrumb" class="mb-3">
								<ol class="breadcrumb">
									<li class="breadcrumb-item">
										<a href="<?php echo esc_url(home_url('/')); ?>" class="text-decoration-none">
											<i class="fas fa-home me-1"></i>
											<?php esc_html_e('Home', 'technews-pro'); ?>
										</a>
									</li>
									<?php
									$categories = get_the_category();
									if (! empty($categories)) :
									?>
										<li class="breadcrumb-item">
											<a href="<?php echo esc_url(get_category_link($categories[0]->term_id)); ?>" class="text-decoration-none">
												<?php echo esc_html($categories[0]->name); ?>
											</a>
										</li>
									<?php endif; ?>
									<li class="breadcrumb-item active" aria-current="page">
										<?php echo wp_trim_words(get_the_title(), 5); ?>
									</li>
								</ol>
							</nav>

							<!-- Post Meta -->
							<div class="post-meta mb-3">
								<div class="d-flex flex-wrap align-items-center gap-3">
									<?php if (! empty($categories)) : ?>
										<span class="badge bg-primary"><?php echo esc_html($categories[0]->name); ?></span>
									<?php endif; ?>

									<small class="text-muted">
										<i class="far fa-calendar-alt me-1"></i>
										<?php echo get_the_date(); ?>
									</small>

									<small class="text-muted">
										<i class="far fa-user me-1"></i>
										<?php the_author_posts_link(); ?>
									</small>

									<small class="text-muted">
										<i class="far fa-eye me-1"></i>
										<?php echo technews_pro_get_post_views(get_the_ID()); ?> <?php esc_html_e('views', 'technews-pro'); ?>
									</small>

									<small class="text-muted">
										<?php echo technews_pro_reading_time(); ?>
									</small>
								</div>
							</div>

							<!-- Post Title -->
							<?php the_title('<h1 class="entry-title h2 mb-4">', '</h1>'); ?>

							<!-- Featured Image -->
							<?php if (has_post_thumbnail()) : ?>
								<div class="post-thumbnail mb-4">
									<figure class="figure w-100">
										<?php the_post_thumbnail('technews-featured', array('class' => 'figure-img img-fluid rounded')); ?>
										<?php
										$caption = get_the_post_thumbnail_caption();
										if ($caption) :
										?>
											<figcaption class="figure-caption text-center mt-2"><?php echo esc_html($caption); ?></figcaption>
										<?php endif; ?>
									</figure>
								</div>
							<?php endif; ?>
						</header>

						<!-- Post Content -->
						<div class="entry-content">
							<?php
							the_content(
								sprintf(
									wp_kses(
										/* translators: %s: Name of current post. Only visible to screen readers */
										__('Continue reading<span class="screen-reader-text"> "%s"</span>', 'technews-pro'),
										array(
											'span' => array(
												'class' => array(),
											),
										)
									),
									wp_kses_post(get_the_title())
								)
							);

							wp_link_pages(
								array(
									'before' => '<div class="page-links mt-4"><span class="page-links-title">' . esc_html__('Pages:', 'technews-pro') . '</span>',
									'after'  => '</div>',
									'link_before' => '<span class="page-number">',
									'link_after'  => '</span>',
								)
							);
							?>
						</div>

						<!-- Post Footer -->
						<footer class="entry-footer mt-5">
							<!-- Tags -->
							<?php
							$tags = get_the_tags();
							if ($tags) :
							?>
								<div class="post-tags mb-4">
									<h6 class="mb-2"><?php esc_html_e('Tags:', 'technews-pro'); ?></h6>
									<div class="d-flex flex-wrap gap-2">
										<?php foreach ($tags as $tag) : ?>
											<a href="<?php echo esc_url(get_tag_link($tag->term_id)); ?>" class="badge bg-secondary text-decoration-none">
												<i class="fas fa-tag me-1"></i>
												<?php echo esc_html($tag->name); ?>
											</a>
										<?php endforeach; ?>
									</div>
								</div>
							<?php endif; ?>

							<!-- Social Share Buttons -->
							<?php technews_pro_social_share_buttons(); ?>

							<!-- Author Bio -->
							<?php
							$author_bio = get_the_author_meta('description');
							if ($author_bio) :
							?>
								<div class="author-bio bg-light p-4 rounded mt-4">
									<div class="d-flex align-items-start">
										<div class="author-avatar me-3">
											<?php echo get_avatar(get_the_author_meta('ID'), 80, '', '', array('class' => 'rounded-circle')); ?>
										</div>
										<div class="author-info">
											<h6 class="author-name mb-1">
												<?php the_author_posts_link(); ?>
											</h6>
											<p class="author-description mb-0 text-muted">
												<?php echo esc_html($author_bio); ?>
											</p>
										</div>
									</div>
								</div>
							<?php endif; ?>
						</footer>
					</article>

					<!-- Post Navigation -->
					<nav class="post-navigation mt-5" aria-label="<?php esc_attr_e('Post navigation', 'technews-pro'); ?>">
						<div class="row">
							<?php
							$prev_post = get_previous_post();
							$next_post = get_next_post();
							?>

							<?php if ($prev_post) : ?>
								<div class="col-md-6 mb-3">
									<div class="nav-previous">
										<div class="card h-100">
											<div class="card-body">
												<small class="text-muted d-block mb-2">
													<i class="fas fa-arrow-<?php echo is_rtl() ? 'right' : 'left'; ?> me-1"></i>
													<?php esc_html_e('Previous Article', 'technews-pro'); ?>
												</small>
												<h6 class="card-title">
													<a href="<?php echo esc_url(get_permalink($prev_post)); ?>" class="text-decoration-none">
														<?php echo esc_html(get_the_title($prev_post)); ?>
													</a>
												</h6>
											</div>
										</div>
									</div>
								</div>
							<?php endif; ?>

							<?php if ($next_post) : ?>
								<div class="col-md-6 mb-3">
									<div class="nav-next">
										<div class="card h-100">
											<div class="card-body text-<?php echo is_rtl() ? 'start' : 'end'; ?>">
												<small class="text-muted d-block mb-2">
													<?php esc_html_e('Next Article', 'technews-pro'); ?>
													<i class="fas fa-arrow-<?php echo is_rtl() ? 'left' : 'right'; ?> ms-1"></i>
												</small>
												<h6 class="card-title">
													<a href="<?php echo esc_url(get_permalink($next_post)); ?>" class="text-decoration-none">
														<?php echo esc_html(get_the_title($next_post)); ?>
													</a>
												</h6>
											</div>
										</div>
									</div>
								</div>
							<?php endif; ?>
						</div>
					</nav>

					<!-- Related Posts -->
					<?php
					$related_posts = get_posts(array(
						'category__in'   => wp_get_post_categories(get_the_ID()),
						'numberposts'    => 4,
						'post__not_in'   => array(get_the_ID()),
						'orderby'        => 'rand',
					));

					if ($related_posts) :
					?>
						<section class="related-posts mt-5">
							<h3 class="h4 mb-4 position-relative">
								<?php esc_html_e('Related Articles', 'technews-pro'); ?>
								<span class="position-absolute bottom-0 start-0 bg-primary" style="height: 3px; width: 50px;"></span>
							</h3>
							<div class="row">
								<?php foreach ($related_posts as $post) : setup_postdata($post); ?>
									<div class="col-lg-6 col-md-6 mb-4">
										<article class="related-post-card">
											<div class="card h-100 border-0 shadow-sm">
												<?php if (has_post_thumbnail()) : ?>
													<a href="<?php the_permalink(); ?>">
														<img src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'technews-grid')); ?>" class="card-img-top" alt="<?php the_title_attribute(); ?>" style="height: 150px; object-fit: cover;">
													</a>
												<?php endif; ?>
												<div class="card-body p-3">
													<small class="text-muted d-block mb-2">
														<i class="far fa-calendar-alt me-1"></i>
														<?php echo get_the_date(); ?>
													</small>
													<h6 class="card-title">
														<a href="<?php the_permalink(); ?>" class="text-decoration-none text-dark">
															<?php the_title(); ?>
														</a>
													</h6>
													<p class="card-text small text-muted">
														<?php echo wp_trim_words(get_the_excerpt(), 12); ?>
													</p>
												</div>
											</div>
										</article>
									</div>
								<?php endforeach;
								wp_reset_postdata(); ?>
							</div>
						</section>
					<?php endif; ?>

					<!-- Comments -->
					<?php
					if (comments_open() || get_comments_number()) :
					?>
						<section class="comments-section mt-5">
							<div class="card">
								<div class="card-header bg-light">
									<h3 class="h5 mb-0">
										<i class="far fa-comments me-2"></i>
										<?php esc_html_e('Comments', 'technews-pro'); ?>
									</h3>
								</div>
								<div class="card-body">
									<?php comments_template(); ?>
								</div>
							</div>
						</section>
					<?php endif; ?>

				<?php endwhile; ?>
			</main><!-- #main -->
		</div>

		<!-- Sidebar -->
		<div class="col-lg-4 col-md-12">
			<?php get_sidebar(); ?>
		</div>
	</div>
</div>

<?php
get_footer();
