<?php

/**
 * TechNews Pro functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package TechNews_Pro
 */

if (! defined('TECHNEWS_PRO_VERSION')) {
	// Replace the version number of the theme on each release.
	define('TECHNEWS_PRO_VERSION', '1.0.0');
}

/**
 * Sets up theme defaults and registers support for various WordPress features.
 *
 * Note that this function is hooked into the after_setup_theme hook, which
 * runs before the init hook. The init hook is too late for some features, such
 * as indicating support for post thumbnails.
 */
function technews_pro_setup()
{
	/*
		* Make theme available for translation.
		* Translations can be filed in the /languages/ directory.
		*/
	load_theme_textdomain('technews-pro', get_template_directory() . '/languages');

	// Add default posts and comments RSS feed links to head.
	add_theme_support('automatic-feed-links');

	/*
		* Let WordPress manage the document title.
		* By adding theme support, we declare that this theme does not use a
		* hard-coded <title> tag in the document head, and expect WordPress to
		* provide it for us.
		*/
	add_theme_support('title-tag');

	/*
		* Enable support for Post Thumbnails on posts and pages.
		*
		* @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
		*/
	add_theme_support('post-thumbnails');

	// Add support for different image sizes for news website
	add_image_size('technews-featured', 800, 450, true);
	add_image_size('technews-slider', 1200, 600, true);
	add_image_size('technews-grid', 400, 250, true);
	add_image_size('technews-thumbnail', 150, 150, true);

	// This theme uses wp_nav_menu() in multiple locations.
	register_nav_menus(
		array(
			'primary' => esc_html__('Primary Menu', 'technews-pro'),
			'footer' => esc_html__('Footer Menu', 'technews-pro'),
			'social' => esc_html__('Social Media Menu', 'technews-pro'),
		)
	);

	/*
		* Switch default core markup for search form, comment form, and comments
		* to output valid HTML5.
		*/
	add_theme_support(
		'html5',
		array(
			'search-form',
			'comment-form',
			'comment-list',
			'gallery',
			'caption',
			'style',
			'script',
		)
	);

	// Set up the WordPress core custom background feature.
	add_theme_support(
		'custom-background',
		apply_filters(
			'technews_pro_custom_background_args',
			array(
				'default-color' => 'f8f9fa',
				'default-image' => '',
			)
		)
	);

	// Add theme support for selective refresh for widgets.
	add_theme_support('customize-selective-refresh-widgets');

	/**
	 * Add support for core custom logo.
	 *
	 * @link https://codex.wordpress.org/Theme_Logo
	 */
	add_theme_support(
		'custom-logo',
		array(
			'height'      => 80,
			'width'       => 300,
			'flex-width'  => true,
			'flex-height' => true,
		)
	);

	// Add support for Block Styles.
	add_theme_support('wp-block-styles');

	// Add support for full and wide align images.
	add_theme_support('align-wide');

	// Add support for editor styles.
	add_theme_support('editor-styles');

	// Enqueue editor styles.
	add_editor_style('style-editor.css');

	// Add support for responsive embedded content.
	add_theme_support('responsive-embeds');

	// Add support for custom line height controls.
	add_theme_support('custom-line-height');

	// Add support for experimental link color control.
	add_theme_support('experimental-link-color');

	// Add support for experimental cover block spacing.
	add_theme_support('custom-spacing');

	// Add support for custom units.
	add_theme_support('custom-units');

	// Remove support for block templates.
	remove_theme_support('block-templates');
}
add_action('after_setup_theme', 'technews_pro_setup');

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function technews_pro_content_width()
{
	$GLOBALS['content_width'] = apply_filters('technews_pro_content_width', 1140);
}
add_action('after_setup_theme', 'technews_pro_content_width', 0);

/**
 * Register widget areas.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function technews_pro_widgets_init()
{
	// Main Sidebar
	register_sidebar(
		array(
			'name'          => esc_html__('Main Sidebar', 'technews-pro'),
			'id'            => 'sidebar-1',
			'description'   => esc_html__('Add widgets here to appear in the main sidebar.', 'technews-pro'),
			'before_widget' => '<section id="%1$s" class="widget %2$s mb-4">',
			'after_widget'  => '</section>',
			'before_title'  => '<h3 class="widget-title h5 mb-3 pb-2 border-bottom border-primary">',
			'after_title'   => '</h3>',
		)
	);

	// Footer Widget Areas
	for ($i = 1; $i <= 4; $i++) {
		register_sidebar(
			array(
				'name'          => sprintf(esc_html__('Footer Widget Area %d', 'technews-pro'), $i),
				'id'            => 'footer-' . $i,
				'description'   => sprintf(esc_html__('Add widgets here to appear in footer column %d.', 'technews-pro'), $i),
				'before_widget' => '<div id="%1$s" class="widget %2$s mb-4">',
				'after_widget'  => '</div>',
				'before_title'  => '<h4 class="widget-title h6 mb-3 text-white">',
				'after_title'   => '</h4>',
			)
		);
	}

	// Header Widget Area (for ads or additional content)
	register_sidebar(
		array(
			'name'          => esc_html__('Header Widget Area', 'technews-pro'),
			'id'            => 'header-widget',
			'description'   => esc_html__('Add widgets here to appear in the header area.', 'technews-pro'),
			'before_widget' => '<div id="%1$s" class="widget %2$s">',
			'after_widget'  => '</div>',
			'before_title'  => '<h4 class="widget-title sr-only">',
			'after_title'   => '</h4>',
		)
	);
}
add_action('widgets_init', 'technews_pro_widgets_init');

/**
 * Enqueue scripts and styles.
 */
function technews_pro_scripts()
{
	// Bootstrap 5.3 RTL CSS
	wp_enqueue_style('bootstrap-rtl', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css', array(), '5.3.2');

	// Font Awesome for icons
	wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');

	// Google Fonts - Tajawal for Arabic and Roboto for English
	wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Roboto:wght@300;400;500;700&display=swap', array(), null);

	// Theme main stylesheet
	wp_enqueue_style('technews-pro-style', get_stylesheet_uri(), array('bootstrap-rtl'), TECHNEWS_PRO_VERSION);
	wp_style_add_data('technews-pro-style', 'rtl', 'replace');

	// Bootstrap 5.3 JavaScript
	wp_enqueue_script('bootstrap-js', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js', array(), '5.3.2', true);

	// Theme custom JavaScript
	wp_enqueue_script('technews-pro-navigation', get_template_directory_uri() . '/js/navigation.js', array('bootstrap-js'), TECHNEWS_PRO_VERSION, true);
	wp_enqueue_script('technews-pro-custom', get_template_directory_uri() . '/js/custom.js', array('jquery', 'bootstrap-js'), TECHNEWS_PRO_VERSION, true);

	// Localize script for AJAX
	wp_localize_script('technews-pro-custom', 'technews_pro_ajax', array(
		'ajax_url' => admin_url('admin-ajax.php'),
		'nonce' => wp_create_nonce('technews_pro_nonce'),
	));

	if (is_singular() && comments_open() && get_option('thread_comments')) {
		wp_enqueue_script('comment-reply');
	}
}
add_action('wp_enqueue_scripts', 'technews_pro_scripts');

/**
 * Implement the Custom Header feature.
 */
require get_template_directory() . '/inc/custom-header.php';

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file.
 */
if (defined('JETPACK__VERSION')) {
	require get_template_directory() . '/inc/jetpack.php';
}

/**
 * Custom functions for TechNews Pro theme
 */

/**
 * Get featured posts for slider
 */
function technews_pro_get_featured_posts($limit = 5)
{
	$args = array(
		'post_type' => 'post',
		'posts_per_page' => $limit,
		'meta_query' => array(
			array(
				'key' => '_thumbnail_id',
				'compare' => 'EXISTS'
			)
		),
		'orderby' => 'date',
		'order' => 'DESC'
	);

	return new WP_Query($args);
}

/**
 * Get latest posts for grid layout
 */
function technews_pro_get_latest_posts($limit = 12, $exclude = array())
{
	$args = array(
		'post_type' => 'post',
		'posts_per_page' => $limit,
		'post__not_in' => $exclude,
		'orderby' => 'date',
		'order' => 'DESC'
	);

	return new WP_Query($args);
}

/**
 * Custom excerpt length
 */
function technews_pro_excerpt_length($length)
{
	return 25;
}
add_filter('excerpt_length', 'technews_pro_excerpt_length', 999);

/**
 * Custom excerpt more
 */
function technews_pro_excerpt_more($more)
{
	return '...';
}
add_filter('excerpt_more', 'technews_pro_excerpt_more');

/**
 * Add social sharing buttons
 */
function technews_pro_social_share_buttons()
{
	global $post;
	$permalink = get_permalink($post->ID);
	$title = get_the_title();

	$facebook_url = 'https://www.facebook.com/sharer/sharer.php?u=' . urlencode($permalink);
	$twitter_url = 'https://twitter.com/intent/tweet?text=' . urlencode($title) . '&url=' . urlencode($permalink);
	$linkedin_url = 'https://www.linkedin.com/sharing/share-offsite/?url=' . urlencode($permalink);
	$whatsapp_url = 'https://wa.me/?text=' . urlencode($title . ' ' . $permalink);

	echo '<div class="social-share-buttons mt-4">';
	echo '<h6 class="mb-3">' . esc_html__('Share this article:', 'technews-pro') . '</h6>';
	echo '<div class="d-flex gap-2">';
	echo '<a href="' . esc_url($facebook_url) . '" target="_blank" class="btn btn-primary btn-sm"><i class="fab fa-facebook-f"></i> Facebook</a>';
	echo '<a href="' . esc_url($twitter_url) . '" target="_blank" class="btn btn-info btn-sm"><i class="fab fa-twitter"></i> Twitter</a>';
	echo '<a href="' . esc_url($linkedin_url) . '" target="_blank" class="btn btn-secondary btn-sm"><i class="fab fa-linkedin-in"></i> LinkedIn</a>';
	echo '<a href="' . esc_url($whatsapp_url) . '" target="_blank" class="btn btn-success btn-sm"><i class="fab fa-whatsapp"></i> WhatsApp</a>';
	echo '</div>';
	echo '</div>';
}

/**
 * Add reading time estimation
 */
function technews_pro_reading_time()
{
	$content = get_post_field('post_content', get_the_ID());
	$word_count = str_word_count(strip_tags($content));
	$reading_time = ceil($word_count / 200); // Average reading speed: 200 words per minute

	if ($reading_time == 1) {
		$timer = esc_html__('1 minute read', 'technews-pro');
	} else {
		$timer = sprintf(esc_html__('%s minutes read', 'technews-pro'), $reading_time);
	}

	return '<span class="reading-time text-muted"><i class="far fa-clock"></i> ' . $timer . '</span>';
}

/**
 * Add view count functionality
 */
function technews_pro_set_post_views($postID)
{
	$count_key = 'post_views_count';
	$count = get_post_meta($postID, $count_key, true);
	if ($count == '') {
		$count = 0;
		delete_post_meta($postID, $count_key);
		add_post_meta($postID, $count_key, '0');
	} else {
		$count++;
		update_post_meta($postID, $count_key, $count);
	}
}

function technews_pro_get_post_views($postID)
{
	$count_key = 'post_views_count';
	$count = get_post_meta($postID, $count_key, true);
	if ($count == '') {
		delete_post_meta($postID, $count_key);
		add_post_meta($postID, $count_key, '0');
		return "0";
	}
	return $count;
}
