/**
 * File navigation.js.
 *
 * TechNews Pro Navigation JavaScript
 * Handles Bootstrap navigation, mobile menu, and accessibility features.
 *
 * @package TechNews_Pro
 */
(function () {
  'use strict';

  // Bootstrap Navigation Handler
  const siteNavigation = document.getElementById('site-navigation');

  // Return early if the navigation doesn't exist.
  if (!siteNavigation) {
    return;
  }

  // Mobile menu toggle functionality
  const mobileToggle = document.querySelector('.navbar-toggler');
  const navbarCollapse = document.querySelector('.navbar-collapse');

  if (mobileToggle && navbarCollapse) {
    // Handle mobile menu toggle
    mobileToggle.addEventListener('click', function () {
      const isExpanded = this.getAttribute('aria-expanded') === 'true';
      this.setAttribute('aria-expanded', !isExpanded);

      // Toggle Bootstrap collapse
      if (typeof bootstrap !== 'undefined') {
        const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
          toggle: true,
        });
      }
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function (event) {
      const isClickInside = siteNavigation.contains(event.target);

      if (!isClickInside && navbarCollapse.classList.contains('show')) {
        if (typeof bootstrap !== 'undefined') {
          const bsCollapse = bootstrap.Collapse.getInstance(navbarCollapse);
          if (bsCollapse) {
            bsCollapse.hide();
          }
        }
        mobileToggle.setAttribute('aria-expanded', 'false');
      }
    });
  }

  // Keyboard navigation support
  const menuLinks = siteNavigation.querySelectorAll('a');
  const dropdownMenus = siteNavigation.querySelectorAll('.dropdown-menu');

  // Handle keyboard navigation
  siteNavigation.addEventListener('keydown', function (event) {
    const activeElement = document.activeElement;
    const menuItems = Array.from(
      siteNavigation.querySelectorAll('.nav-link, .dropdown-item')
    );
    const currentIndex = menuItems.indexOf(activeElement);

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        if (currentIndex < menuItems.length - 1) {
          menuItems[currentIndex + 1].focus();
        }
        break;

      case 'ArrowUp':
        event.preventDefault();
        if (currentIndex > 0) {
          menuItems[currentIndex - 1].focus();
        }
        break;

      case 'Escape':
        // Close any open dropdowns
        dropdownMenus.forEach(menu => {
          if (menu.classList.contains('show')) {
            const dropdown = bootstrap.Dropdown.getInstance(
              menu.previousElementSibling
            );
            if (dropdown) {
              dropdown.hide();
            }
          }
        });
        break;
    }
  });

  // Search functionality enhancements
  const searchToggle = document.querySelector(
    '[data-bs-target="#searchCollapse"]'
  );
  const searchCollapse = document.getElementById('searchCollapse');

  if (searchToggle && searchCollapse) {
    searchToggle.addEventListener('click', function () {
      setTimeout(() => {
        const searchInput = searchCollapse.querySelector(
          'input[type="search"]'
        );
        if (searchInput && searchCollapse.classList.contains('show')) {
          searchInput.focus();
        }
      }, 300);
    });
  }

  // Handle window resize
  window.addEventListener('resize', function () {
    // Close mobile menu on desktop
    if (
      window.innerWidth >= 992 &&
      navbarCollapse &&
      navbarCollapse.classList.contains('show')
    ) {
      const bsCollapse = bootstrap.Collapse.getInstance(navbarCollapse);
      if (bsCollapse) {
        bsCollapse.hide();
      }
    }
  });
})();
